import React, { useEffect, useRef, useState } from 'react';

const CustomPopover = ({
  open,
  anchorEl,
  onClose,
  children,
  anchorOrigin = { vertical: 'bottom', horizontal: 'left' },
  transformOrigin = { vertical: 'top', horizontal: 'left' },
  style = {},
  paperStyle = {},
  disableBackdropClick = false,
  disableEscapeKeyDown = false,
}) => {
  const popoverRef = useRef(null);
  const [position, setPosition] = useState({ top: 0, left: 0 });

  // 计算位置
  const calculatePosition = () => {
    if (!anchorEl || !popoverRef.current) return;

    const anchorRect = anchorEl.getBoundingClientRect();
    const popoverRect = popoverRef.current.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let top = 0;
    let left = 0;

    // 根据 anchorOrigin 计算基础位置
    switch (anchorOrigin.vertical) {
      case 'top':
        top = anchorRect.top;
        break;
      case 'center':
        top = anchorRect.top + anchorRect.height / 2;
        break;
      case 'bottom':
      default:
        top = anchorRect.bottom;
        break;
    }

    switch (anchorOrigin.horizontal) {
      case 'left':
        left = anchorRect.left;
        break;
      case 'center':
        left = anchorRect.left + anchorRect.width / 2;
        break;
      case 'right':
        left = anchorRect.right;
        break;
      default:
        left = anchorRect.left;
        break;
    }

    // 根据 transformOrigin 调整位置
    switch (transformOrigin.vertical) {
      case 'top':
        // top 保持不变
        break;
      case 'center':
        top -= popoverRect.height / 2;
        break;
      case 'bottom':
        top -= popoverRect.height;
        break;
    }

    switch (transformOrigin.horizontal) {
      case 'left':
        // left 保持不变
        break;
      case 'center':
        left -= popoverRect.width / 2;
        break;
      case 'right':
        left -= popoverRect.width;
        break;
    }

    // 边界检查，确保不超出视口
    if (left + popoverRect.width > viewportWidth) {
      left = viewportWidth - popoverRect.width - 10;
    }
    if (left < 10) {
      left = 10;
    }
    if (top + popoverRect.height > viewportHeight) {
      top = viewportHeight - popoverRect.height - 10;
    }
    if (top < 10) {
      top = 10;
    }

    setPosition({ top, left });
  };

  // 处理点击外部关闭
  const handleBackdropClick = (e) => {
    if (!disableBackdropClick && popoverRef.current && !popoverRef.current.contains(e.target) && !anchorEl?.contains(e.target)) {
      onClose && onClose(e, 'backdropClick');
    }
  };

  // 处理 ESC 键关闭
  const handleKeyDown = (e) => {
    if (!disableEscapeKeyDown && e.key === 'Escape') {
      onClose && onClose(e, 'escapeKeyDown');
    }
  };

  useEffect(() => {
    if (open) {
      calculatePosition();
      document.addEventListener('mousedown', handleBackdropClick);
      document.addEventListener('keydown', handleKeyDown);
      window.addEventListener('resize', calculatePosition);
      window.addEventListener('scroll', calculatePosition);
    }

    return () => {
      document.removeEventListener('mousedown', handleBackdropClick);
      document.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('resize', calculatePosition);
      window.removeEventListener('scroll', calculatePosition);
    };
  }, [open, anchorEl]);

  useEffect(() => {
    if (open && anchorEl) {
      // 延迟计算位置，确保内容已渲染
      setTimeout(calculatePosition, 0);
    }
  }, [open, anchorEl, children]);

  if (!open) return null;

  return (
    <div
      ref={popoverRef}
      style={{
        position: 'fixed',
        top: position.top,
        left: position.left,
        zIndex: 2147483647,
        backgroundColor: 'white',
        border: '1px solid #e0e0e0',
        borderRadius: '4px',
        boxShadow: '0px 5px 5px -3px rgba(0,0,0,0.2), 0px 8px 10px 1px rgba(0,0,0,0.14), 0px 3px 14px 2px rgba(0,0,0,0.12)',
        maxHeight: '400px',
        overflowY: 'auto',
        overflowX: 'hidden',
        ...style,
        ...paperStyle,
      }}
      onClick={(e) => e.stopPropagation()}
    >
      {children}
    </div>
  );
};

export default CustomPopover;
