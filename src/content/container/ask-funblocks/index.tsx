import { MutableRefObject, useEffect, useRef, useState } from 'react'
import ReactDraggable from 'react-draggable'
import { useSelectionManager } from '../store/selection'
import { action_bar_widget_id, useView } from '../store/view'
import { Content } from './content'
import { useDispatch, useSelector } from 'react-redux'
import { CLOSE_WIDGET_DIALOG } from '@/common/constants/actionTypes'
import FlowEditor from '@/components/Flow/FlowEditor'

let fixedRef: MutableRefObject<HTMLDivElement>

export const AskFunBlocks: React.FC = () => {
  const selectionManager = useSelectionManager()
  const dispatch = useDispatch()
  const { position } = selectionManager
  const { viewStatus, goToInputPage, objType, generator } = useView()
  const _fixedRef = useRef<HTMLDivElement>()
  const closeWidgetModalState =
    useSelector((state) => state.uiState.closeWidgetDialog) || {}
  const [close_widget_modal_state, set_close_widget_modal_state] = useState()
  const [pos, setPos] = useState(position)
  const structured_generated_content = useSelector(
    (state) => state.uiState.structured_generated_content
  )

  fixedRef = _fixedRef

  useEffect(() => {
    if (
      closeWidgetModalState?.isModalOpen ||
      ![action_bar_widget_id].includes(closeWidgetModalState.widget) ||
      !closeWidgetModalState.data
    ) {
      return
    }

    set_close_widget_modal_state(closeWidgetModalState)

    dispatch({
      type: CLOSE_WIDGET_DIALOG,
      value: {
        isModalOpen: false,
      },
    })
  }, [closeWidgetModalState])

  useEffect(() => {
    setPos(position)
  }, [position])

  useEffect(() => {
    if (structured_generated_content?.content) {
      goToInputPage({ objType, generator })
    }
  }, [structured_generated_content])

  useEffect(() => {
    const resizeObserver = new ResizeObserver((entries) => {
      const rect = _fixedRef?.current?.getBoundingClientRect()
      if (!rect) return

      if (rect.bottom > window.innerHeight) {
        setPos((prevState) => {
          let y = prevState.y - (rect.bottom - window.innerHeight)
          if (y < 30) {
            y = 30
          }

          return {
            x: prevState.x,
            y,
          }
        })
      }
    })

    if (_fixedRef?.current) {
      resizeObserver.observe(_fixedRef.current)
    }

    return () => {
      resizeObserver.disconnect()
    }
  }, [_fixedRef?.current])

  if (viewStatus === 'none') {
    return null
  }

  const content = (
    <div
      ref={_fixedRef}
      style={{
        position: 'fixed',
        top: `${structured_generated_content ? 0 : pos.y}px`,
        left: `${structured_generated_content ? 0 : pos.x}px`,
        zIndex: 2147483646,
      }}
    >
      <Content close_widget_modal_state={close_widget_modal_state} />
    </div>
  )

  if (structured_generated_content?.content) {
    return (
      <FlowEditor
        content={structured_generated_content.content}
        ai_action={structured_generated_content.ai_action}
      />
    )
  }

  if (viewStatus === 'icon') {
    return content
  }

  return <ReactDraggable handle=".handle">{content}</ReactDraggable>
}

export const getFixedDom = () => {
  return fixedRef.current
}
