import { Logo } from "@/components/icon"
import { useState } from "react";
import { createContainer } from "unstated-next";
import { Close } from '@styled-icons/material/Close'
import { useIntl } from "react-intl";
import { Tooltip } from "@mui/material";
import { isLinkedIn } from "@/content/utils/content-dom";
import { ProductFeatures } from "@/common/product-features";

export const writerButtonElementId = ProductFeatures.getProductId('ai-writer-widget');
const { useContainer: useAIWriterButton, Provider: AIWriterButtonProvider } = createContainer(
    () => {
        const writerButtonMirrorElementId = ProductFeatures.getProductId('ai-writer-widget-cover-div')
        const intl = useIntl();

        const AIWriterButtonWidget = ({ targetEditor }) => {
            const [hovered, setHovered] = useState();
            return <div
                id={writerButtonMirrorElementId}
                style={{
                    position: 'absolute',
                    pointerEvents: 'none',
                    width: targetEditor.getBoundingClientRect()?.width,
                    height: targetEditor.getBoundingClientRect()?.height,
                    minHeight: isLinkedIn ? 24 : 28,
                }}>
                <div
                    style={{
                        position: 'absolute',
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: hovered ? 'dodgerblue' : undefined,
                        right: 1,
                        bottom: 1,
                        borderRadius: 30,
                        pointerEvents: 'all',
                        columnGap: 3,
                        padding: 2,
                    }}
                    onMouseLeave={() => {
                        setHovered(null);
                    }}
                >
                    {
                        hovered &&
                        <div
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: 22,
                                height: 22,
                                borderRadius: 20,
                                backgroundColor: hovered?.id == 'close' ? 'deepskyblue' : 'dodgerblue',
                                color: '#ddd',
                                cursor: 'pointer',
                                userSelect: 'none'
                            }}
                            onClick={() => {
                                window.postMessage({ type: 'launch_switch_widget_modal', data: { widget: writerButtonElementId } }, '*');
                            }}

                            onMouseEnter={() => {
                                setHovered({ id: 'close' });
                            }}
                        >
                            <Close size={16} />
                        </div>
                    }
                    <Tooltip title={intl.formatMessage({ id: 'ai_writing_assistant' })}>
                    {/* <Tooltip title={'ai_writing_assistant'}> */}
                        <div
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: 22,
                                height: 22,
                                borderRadius: 20,
                                backgroundColor: hovered?.id == 'ai_writer' ? 'deepskyblue' : undefined,
                                color: 'white',
                                cursor: 'pointer',
                                userSelect: 'none'
                            }}
                            onClick={() => {
                                window.postMessage({ type: 'launch_ai_write_assistant' }, '*');
                            }}
                            onMouseEnter={() => {
                                setHovered({ id: 'ai_writer' });
                            }}

                        >
                            {/* <Logo size={14} /> */}
                            {hovered?.id == 'ai_writer' && <Logo size={14} />}
                            {hovered?.id != 'ai_writer' &&
                                <div style={{
                                    backgroundColor: 'dodgerblue',
                                    borderRadius: 10,
                                    width: 10,
                                    height: 10
                                }}>
                                </div>
                            }

                        </div>
                    </Tooltip>
                </div>
            </div>
        }


        return {
            writerButtonElementId,
            writerButtonMirrorElementId,
            AIWriterButtonWidget,
        }
    }
)

export { useAIWriterButton, AIWriterButtonProvider }