import { Divider, Toolt<PERSON>, Popover } from '@mui/material';
import { Checkbox, Modal } from 'antd';
import * as React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { addNote, callAIAssist, callAnthropic, callOpenAI, callPaLM, fetchPrompts, fetchPromptsPinned, fetchPromptsWorkspace, getAppConfig, getLngList, getUserInfo, refreshAIResponse, shareAIContent, upsertDoc } from 'src/common/actions/ticketAction';
import cx from 'classnames';
import { AI_ASSISTANT_DIALOG, APP_CONFIG, CONFIRM_DIALOG, SETTINGS_DIALOG, STRUCTURED_GENERATED_CONTENT } from 'src/common/constants/actionTypes';
import { Magic } from '@styled-icons/bootstrap/Magic'
import { useIntl } from 'react-intl';
import mdit from 'markdown-it'
import hljsPlugin from 'markdown-it-highlightjs'
import { Warning } from '@styled-icons/fluentui-system-regular/Warning'
import { CheckboxUnchecked } from '@styled-icons/fluentui-system-regular/CheckboxUnchecked'
import { CheckboxChecked } from '@styled-icons/fluentui-system-regular/CheckboxChecked'
import { Check } from '@styled-icons/material/Check'

import { ContentCopy, KeyboardReturn, Refresh, Share } from '@styled-icons/material';
import { Selector } from "@/common/selector";
import { getStateByUser } from 'src/common/reducers/listReducer';
import { useSelectionManager } from '../../store/selection';
import browser from 'webextension-polyfill';
import { EventName } from '@/common/event-name';
import ConfirmMessage from '../confirm_message';
import { ThreeDots } from 'react-loader-spinner'
import { useSettings } from '@/common/store/settings';
import { funblocks_domain, geminiStreamGenerate, openAIStreamGenerate } from '@/common/serverAPIUtil';
import { SharePopover } from '../result-panel/actions/share';
import { cloneDeep } from 'lodash-es';
import { extractWebContent } from '@/common/article_extractor';
import { isGoogleDocs } from '@/content/utils/content-dom';
import { Close } from "@styled-icons/material";
import { llm_providers } from '@/options/setting-form/llm-provider';
import ContentEditable from 'react-contenteditable';
import TurndownService from 'turndown';
import ModelSelector from './model-selector';
import { Copy } from '@styled-icons/fluentui-system-regular';
import { ArrowToBottom } from '@styled-icons/boxicons-regular/ArrowToBottom';
import { ArrowSwap } from '@styled-icons/fluentui-system-regular';
import { Wand } from '@styled-icons/fluentui-system-filled/Wand'
import { ArrowMaximizeVertical } from '@styled-icons/fluentui-system-regular/ArrowMaximizeVertical'
import { ArrowMinimizeVertical } from '@styled-icons/fluentui-system-regular/ArrowMinimizeVertical'
import { Translate } from '@styled-icons/bootstrap/Translate'
import { ArrowBack } from '@styled-icons/boxicons-regular/ArrowBack'
import { Expand } from '@styled-icons/material/Expand'
import { Save } from '@styled-icons/boxicons-regular/Save'
import { extractJSONFromString } from '@/common/jsonStringUtil';
import MentalModelSelector from './mental-model-selector';
// import { RWebShare } from "react-web-share";

const md = mdit().use(hljsPlugin);

const TEXT_LEN_LIMIT = 6000;

const AI_API_CALLER = {
  openai: callOpenAI,
  groq: callOpenAI,
  openai_compatible: callOpenAI,
  anthropic: callAnthropic,
  gemini: callPaLM,
}

const removeQuotes = (str) => {
  if (!str) {
    return "";
  }

  return str.replace(/^```/, "").replace(/```$/, "");
}

const removeDataURLPrefix = (dataURL) => {
  if (!dataURL) return '';
  const dataURLRegex = /^data:([^;]+);base64,/;
  return dataURL.replace(dataURLRegex, '');
}

const AIForm = ({ generator, hide, objType, init_action, sub_item_action, drafter, selected_content }) => {
  const dialogState = useSelector(state => state.uiState.aiDialog) || {};

  const loginUser = useSelector(state => state.loginIn && state.loginIn.user);

  const prompts = useSelector(state => state.prompts);
  const prompt_lists = useSelector(state => getStateByUser(state.prompt_lists, loginUser));
  const pinned_prompt_lists = useSelector(state => getStateByUser(state.pinned_prompt_lists, loginUser));
  const workspace_prompt_lists = useSelector(state => getStateByUser(state.workspace_prompt_lists, loginUser));
  const public_prompt_lists = useSelector(state => getStateByUser(state.public_prompt_lists, loginUser));
  const ai_use_search = useSelector(state => state.uiState.ai_use_search);
  const { settings: aiSettings, refresh, setSettings } = useSettings();

  const [sub_menu_anchor, set_sub_menu_anchor] = React.useState();
  const [popoverAnchorEl, setPopoverAnchorEl] = React.useState(null);
  const dispatch = useDispatch();
  const intl = useIntl();
  const textInputRef = React.useRef();
  const selectedItemRef = React.useRef(null);
  const contentContainerRef = React.useRef();
  const mainDiv = React.useRef();
  const mainInnerDiv = React.useRef();

  const app_config = useSelector(state => state.uiState.app_config);
  const assistant_items = app_config?.assistant_items;
  const assistant_items_groups = app_config?.assistant_items_groups;
  const [ai_items, set_ai_items] = React.useState([]);

  const [searchText, setSearchText] = React.useState('');
  const [userInput, setUserInput] = React.useState();
  const [itemTargeted, setItemTargeted] = React.useState(0);
  const [sub_menu_parent, set_sub_menu_parent] = React.useState(-1);
  const [sub_menu_items, set_sub_menu_items] = React.useState([]);
  const [sub_menu_item_targeted, set_sub_menu_item_targeted] = React.useState(0);
  const [sub_menu_visible, set_sub_menu_visible] = React.useState(false);
  const [avaliableAssistantItems, setAvaliableAssistantItems] = React.useState(assistant_items);
  const [extendedAssistantItems, setExtendedAssistantItems] = React.useState([]);
  const [filteredAssistantItems, setFilteredAssistantItems] = React.useState([]);
  const [groupedAssistantItems, setGroupedAssistantItems] = React.useState([]);
  const [confirmMessage, setConfirmMessage] = React.useState();
  const [doneMessage, setDoneMessage] = React.useState();
  const [form, setForm] = React.useState();
  const [isInputZh, setIsInputZh] = React.useState();
  const [extendInput, setExtendInput] = React.useState();
  const [mentalModel, setMentalModel] = React.useState({
    value: 'none',
    label: intl.formatMessage({ id: 'none' })
  });

  const [selectedText, setSelectedText] = React.useState();
  const [textInited, setTextInited] = React.useState();
  const [readyToDoInitAction, setReadyToDoInitAction] = React.useState()
  const [context, setContext] = React.useState();
  const usingTitle = useSelector(state => state.uiState.titleSelected);
  const [errMsg, setErrMsg] = React.useState();
  const [errMsgId, setErrMsgId] = React.useState();
  const [contextErrMsg, setContextErrMsg] = React.useState();
  const form_input_ref = React.useRef();
  const mdContainerRef = React.useRef();
  const containerRef = React.useRef();
  const selectionManager = useSelectionManager();
  const selectedContent = selected_content || selectionManager.getSelectedContent();
  const isRIL = ['article', 'codes'].includes(selectedContent?.type);
  const [attach_selected_content, set_attach_selected_content] = React.useState();
  const lng_list_origin = useSelector(state => state.uiState.lng_list);
  const [lng_list, set_lng_list] = React.useState();
  const [lang, setLang] = React.useState(aiSettings?.ai_respond_lng == 'cn' ? 'zh' : aiSettings?.ai_respond_lng);

  const [width, setWidth] = React.useState(560);
  const [height, setHeight] = React.useState();
  const [main_inner_max_height, set_main_inner_max_height] = React.useState(280);
  const [isResizing, setIsResizing] = React.useState(false);

  const llm_api_keys = aiSettings?.llm_api_keys;
  const ai_api_model = aiSettings?.ai_api_model;
  const [temp_llm_model, set_temp_llm_model] = React.useState();

  const using_model = React.useMemo(() => {
    const model_id = temp_llm_model || ai_api_model;
    let model = llm_api_keys?.find(item => item.id === model_id) || app_config?.ai_api_models?.find(item => item.value === model_id);

    return model;
  }, [app_config?.ai_api_models, llm_api_keys, ai_api_model, temp_llm_model])

  React.useEffect(() => {
    refresh();
    dispatch(getLngList({ locale: aiSettings.lang }));
    dispatch(getUserInfo({ locale: aiSettings.lang }));
    dispatch(getAppConfig({ service: 'extension', locale: aiSettings.lang }, (data) => {
      dispatch({
        type: APP_CONFIG,
        value: data
      })
    }));

    if (generator === 'mindmap') {
      return;
    }

    dispatch(fetchPrompts({}));
    dispatch(fetchPromptsPinned({}));
  }, []);

  React.useEffect(() => {
    if (!lng_list_origin?.length) return;

    let new_list = [...lng_list_origin];
    new_list.unshift({
      Symbol: 'as_context',
      label: intl.formatMessage({ id: 'set_lang_as_context' })
    })

    set_lng_list(new_list);
  }, [lng_list_origin, intl])

  React.useEffect(() => {
    let lng = aiSettings?.ai_respond_lng == 'cn' ? 'zh' : aiSettings?.ai_respond_lng;

    if (!['ril', 'image'].includes(objType) && !['explain', 'translate'].includes(init_action)) {
      lng = 'as_context';
    }

    setLang(lng);
  }, [init_action, aiSettings?.ai_respond_lng, selectedContent])

  React.useEffect(() => {
    if (!userInput) {
      return setSearchText('');
    }

    const turndownService = new TurndownService();
    const mrkd = turndownService.turndown(userInput)?.trim();

    setSearchText(mrkd)
  }, [userInput])

  const prompt_to_ai_item = React.useCallback((prompt) => {
    return {
      label: prompt.name,
      type: 'user_installed',
      action: prompt._id,
      prompt_user: prompt.prompt + (prompt.content_source == 'selected' ? '.\nGiven text: {{selected_text}}' : ''),
      args: prompt.args || [],
      objTypes: ['markdown', 'slides', 'ril']
    }
  }, []);

  const get_ai_prompt_item = React.useCallback((action) => {
    let item = assistant_items?.find(it => it.action == action);
    if (item) return item;

    item = prompt_lists?.items?.find(it => it._id == action) ||
      pinned_prompt_lists?.items?.find(it => it._id == action) ||
      public_prompt_lists?.items?.find(it => it._id == action) ||
      prompts?.byId[action];

    if (!item) return;

    return prompt_to_ai_item(item);
  }, [assistant_items, prompt_lists, pinned_prompt_lists, public_prompt_lists, prompts])

  React.useEffect(() => {
    if (generator === 'mindmap') {
      return;
    }

    let items = [];

    assistant_items?.forEach(item => {
      let index = items.findIndex(it => it.action === item._id);
      if (index == -1) {
        items.push(item);
      }
    })

    let my_prompts = prompt_lists?.items
    //TODO: move it back when build customer Propmt UX optimized
    items = items.filter(item => item.action !== 'add_prompt');
    if (my_prompts?.length > 0) {
      // items = items.filter(item => item.action !== 'add_prompt');
      my_prompts.forEach(item => {
        let assist_item = prompt_to_ai_item(item);
        assist_item.group = 9;

        let index = items.findIndex(it => it.action === item._id);
        if (index > -1) {
          items[index] = assist_item;
        } else {
          items.push(assist_item);
        }
      })
    }

    if (pinned_prompt_lists?.items?.length > 0) {
      pinned_prompt_lists.items.forEach(item => {
        let assist_item = prompt_to_ai_item(item);
        assist_item.group = 8;

        let index = items.findIndex(it => it.action === item._id);
        if (index > -1) {
          items[index] = assist_item;
        } else {
          items.push(assist_item);
        }
      })
    }

    if (workspace_prompt_lists?.items?.length > 0) {
      workspace_prompt_lists.items.forEach(item => {
        let assist_item = prompt_to_ai_item(item);
        assist_item.group = 7;

        let index = items.findIndex(it => it.action === item._id);
        if (index > -1) {
          items[index] = assist_item;
        } else {
          items.push(assist_item);
        }
      })
    }

    set_ai_items(items);
    // setDrafter(items.find(item => item.action == 'draft'));
  }, [prompt_lists, pinned_prompt_lists, workspace_prompt_lists, assistant_items])

  React.useEffect(() => {
    // if (!dialogState.visible) {
    //   setInitText('');

    //   if (loading) {
    //     setCancelled(doing);
    //   }


    //   setDoing(null);
    //   setEntranceWithAI(false);

    //   setCopied(false);
    //   setForm(null);
    //   setContext(null);

    //   return;
    // }

    // if (dialogState.caller === 'plate' && !editor) return;

    // setTitleSelected(false);
    setAiResponse(null);
    setPrevAIResponse(null);
    setUserInput(dialogState.query || '');
    setLoading(false);
    setErrMsg('');
    setErrMsgId(null);
    setContextErrMsg(null);
    setAiResponse(null);
    setCopied(false);
    setForm(null);
    setContext(null);
    setCancelled(null);
    setConfirmMessage(null);

    let selectTxt;

    if (selectedContent) {
      selectTxt = selectedContent.title || '';

      let sender = selectedContent.sender ? '[sender]: ' + selectedContent.sender : ''
      let title = selectedContent.title ? '[title]: ' + selectedContent.title : '';
      let platform = selectedContent.drafter ? '[social platform]: ' + selectedContent.drafter : '';
      let content = selectedContent.text?.trim() ? ((selectedContent.sender || selectedContent.title || platform) ? '[content]: ' : '') + selectedContent.text?.trim() : '';

      selectTxt = `${sender}\n\n${platform}\n\n${title}\n\n${content}`;
    }
    setSelectedText(selectTxt);

    setTextInited(true);
  }, [objType]);

  React.useEffect(() => {
    set_attach_selected_content(selectedText?.trim())
  }, [selectedText])

  React.useEffect(() => {
    // console.log('set readyTodoInitAction........', textInited, assistant_items?.length > 0)
    setReadyToDoInitAction(textInited && (assistant_items?.length > 0 || generator === 'mindmap'))
  }, [textInited, assistant_items?.length > 0, generator])

  React.useEffect(() => {
    if (readyToDoInitAction) {
      if (init_action) {
        if (sub_item_action) {
          const parent = assistant_items.find(item => item.action == init_action);
          const item = parent.sub_items.find(item => item.value === sub_item_action)
          sub_menu_item_clicked({ parent, item })
        } else if (init_action == 'translate') {
          const parent = assistant_items.find(item => item.action == init_action);
          const target_lng = aiSettings.ai_respond_lng || aiSettings.lang;
          const lng = lng_list?.find(l => l.Symbol == (target_lng == 'cn' ? 'zh' : target_lng));

          const item = parent.sub_items.find(item => item.id == (lng?.intl_key || 'english'))
          sub_menu_item_clicked({ parent, item })
        } else {
          itemSelected({ action: init_action });
        }
      }
    }
  }, [readyToDoInitAction])

  React.useEffect(() => {
    if (!ai_items) {
      return;
    }

    if (objType == 'markdown' && !selectedText?.trim()) {
      return setAvaliableAssistantItems(ai_items.filter(item => item.group === 3 || (item.group === 9 || item.type == 'user_installed')));
    }

    setAvaliableAssistantItems(ai_items);
  }, [ai_items, selectedText])

  React.useEffect(() => {
    if (!avaliableAssistantItems) return;
    const extended = avaliableAssistantItems
      .filter(item => objType == 'ril' && item.group == 3 && item.objTypes.includes('markdown') || item.action === init_action || item.objTypes.includes(objType || 'markdown'))
    setExtendedAssistantItems(extended);
  }, [avaliableAssistantItems, objType]);

  const isFillingForm = () => {
    if (!form) {
      return false;
    }

    return form.args && form.args.findIndex(arg => !!arg.value) > -1
  }

  const handleClose = (forced, clickedOutside) => {
    if (!!forced || !clickedOutside && aiSettings?.give_up_ai_content_directly || (!aiResponse && !loading && !searchText && !isFillingForm())) {
      hide();
    } else {
      return setConfirmMessage({
        onNeverShow: clickedOutside ? undefined : () => setSettings({
          ...aiSettings,
          give_up_ai_content_directly: true
        }),
        onConfirm: () => handleClose(true),
        content: intl.formatMessage({ id: loading ? 'confirm_no_wait' : 'confirm_close_ai' })
      })
    }
  }

  const handleBack = (forced) => {
    if (!forced && !aiSettings?.give_up_ai_content_directly && (loading || aiResponse && !aiResponse.err || isFillingForm())) {
      return setConfirmMessage({
        onNeverShow: () => setSettings({
          ...aiSettings,
          give_up_ai_content_directly: true
        }),
        onConfirm: () => handleBack(true),
        content: intl.formatMessage({ id: loading ? 'confirm_no_wait' : 'confirm_discard_ai_content' })
      })
    }

    if (loading) {
      setCancelled(doing);
    }

    if (!doing || !aiResponse && form) {
      setForm(null);
      setContext(null);
      set_attach_selected_content(selectedText?.trim())
      // set_attach_selected_content(selectedContent?.text)
    }

    if (doing?.action === 'query' && doing.userInput) {
      setUserInput(doing.userInput);
    }

    set_main_inner_max_height(280);
    setHeight();

    setLoading(false);
    setDoing(null);
    setAiResponse(null);
    setErrMsg(null);
    setErrMsgId(null);
    setConfirmMessage(null);
  }

  React.useEffect(() => {
    selectedItemRef.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
      inline: 'center'
    });
  }, [selectedItemRef]);

  const elmRefs = React.useRef({});
  React.useLayoutEffect(() => {
    if (!elmRefs) return;

    elmRefs[itemTargeted] && elmRefs[itemTargeted].current.scrollIntoView({
      behavior: 'smooth',
      block: 'end',
    });

    const selectedItem = elmRefs.current[itemTargeted];
    selectedItem && selectedItem.scrollIntoView({
      behavior: 'smooth',
      block: 'end',
    });

    close_sub_menu()
    // let item = groupedAssistantItems.find(it => it.order == itemTargeted);
    // set_sub_menu_items(item?.sub_items);

  }, [itemTargeted]);

  const sub_menu_item_refs = React.useRef([]);
  React.useLayoutEffect(() => {
    if (!sub_menu_item_refs) return;

    sub_menu_item_refs[sub_menu_item_targeted] && sub_menu_item_refs[sub_menu_item_targeted].current.scrollIntoView({
      behavior: 'smooth',
      block: 'end',
    });

    const selectedItem = sub_menu_item_refs.current[sub_menu_item_targeted];
    selectedItem && selectedItem.scrollIntoView({
      behavior: 'smooth',
      block: 'end',
    });
  }, [sub_menu_item_targeted]);

  React.useEffect(() => {
    set_sub_menu_visible(sub_menu_anchor && !!sub_menu_items);
  }, [sub_menu_anchor, sub_menu_items])

  React.useEffect(() => {
    if (sub_menu_visible) return;

    textInputRef?.current?.focus();
  }, [sub_menu_visible])

  const [confirm_option_switcher, set_confirm_option_switcher] = React.useState();
  const [confirm_option_enter, set_confirm_option_enter] = React.useState();

  React.useEffect(() => {
    if (confirmMessage) return;

    set_confirm_option_enter(0);
    set_confirm_option_switcher(0);
  }, [confirmMessage])

  const open_sub_menu = React.useCallback((parent_item) => {
    let item = groupedAssistantItems.find(it => it.order == parent_item);
    if (!item?.sub_items) {
      return false;
    }

    if (sub_menu_visible && sub_menu_parent?.action === item.action) {
      close_sub_menu()
      return false
    }

    set_sub_menu_parent(item);
    set_sub_menu_items(item?.sub_items);
    set_sub_menu_anchor(elmRefs.current[parent_item]);

    // set_sub_menu_visible(true);

    return true;
  }, [groupedAssistantItems, elmRefs, sub_menu_visible, sub_menu_anchor]);

  const itemSelected = ({ order, action, callback }) => {
    let item = !action ? groupedAssistantItems.find(it => it.order == order) : (ai_items?.find(it => it.action == action) || groupedAssistantItems?.find(it => it.action == action) || assistant_items?.find(it => it.action == action));
    // item = item || {
    //   action: init_action
    // }
    // console.log('item selected..........', {action, drafter, item})

    if (!item) {
      return;
    }

    if (item.action === 'add_prompt') {
      callback && callback();
      gotoPrompts()
      return;
    }

    if (typeof item.action === 'function') {
      item.action(callback || (() => { }));

      if (item.id !== 'save_to_memo' && item.id != 'copy') {
        callback && callback();
      }
      return;
    } else if (item.sub_items) {
      if (action === 'draft' && drafter) {
        let subItem = item.sub_items?.find(it => it.value == 'draft_' + drafter);
        if (!subItem) {
          subItem = item.sub_items?.find(it => it.value == 'draft_more');
        }

        return sub_menu_item_clicked({ parent: item, item: subItem })
      }

      open_sub_menu(order);
      callback && callback();
      return;
    }

    callback && callback();

    if (item.args?.length > 0 || item.type == 'user_installed' && (!selectedText?.trim() && item.prompt_user?.includes('{{selected_text}}'))) {
      setUserInput('');

      let args = item.args || [];
      if (selectedContent?.keypoints || selectedContent?.topic) {
        args = args.map(arg => {
          if (['outline', 'reply_outline'].includes(arg.name)) {
            return {
              ...arg,
              value: selectedContent.keypoints
            }
          } else if (arg.name === 'topic') {
            return {
              ...arg,
              value: selectedContent.topic
            }
          }

          return arg;
        })
      }

      setForm({
        item,
        args
      })
      textInputRef?.current?.focus()
      return;
    }

    aiAction({ action: item.action, aiResponse, disableContext: true });
  }

  const sub_menu_item_clicked = ({ parent, item }) => {
    close_sub_menu();

    if (item.args?.length > 0 || parent.args?.length > 0
      || item.type == 'user_installed' && (!selectedText?.trim() && item.prompt_user?.includes('{{selected_text}}')) ||
      parent.type == 'user_installed' && (!selectedText?.trim() && parent.prompt_user?.includes('{{selected_text}}'))) {
      setUserInput('');

      let args = item.args || parent.args || [];
      if (selectedContent?.keypoints || selectedContent?.topic) {
        args = args.map(arg => {
          if (['outline', 'reply_outline'].includes(arg.name)) {
            return {
              ...arg,
              value: selectedContent.keypoints
            }
          } else if (arg.name === 'topic') {
            return {
              ...arg,
              value: selectedContent.topic
            }
          }

          return arg;
        })
      }

      setForm({
        item: parent,
        sub_item: item.value == 'draft_more' ? { ...item, label: drafter } : item,
        args
      });

      return;
    }

    aiAction({ action: parent.action, sub_item: item, aiResponse, disableContext: true })
  }

  React.useEffect(() => {
    if (!form?.sub_item?.prompt_user && !form?.item?.prompt_user) return;

    if (!(form.sub_item?.prompt_user || form.item?.prompt_user).includes('{{selected_text}}')) {
      return;
    }

    setForm(prevForm => {
      let args = prevForm.args || [];
      let dynamic_arg = args.find(arg => arg.name == 'selected_text');
      if (dynamic_arg) {
        dynamic_arg.disabled = !!attach_selected_content;
      } else {
        args.unshift({
          disabled: !!attach_selected_content,
          name: 'selected_text',
          type: 'text',
          label: intl.formatMessage({ id: 'ask_ai_about_selected' }),
          required: true,
          hint: intl.formatMessage({ id: 'ask_ai_about_given_text_hint' })
        });
      }

      return {
        ...prevForm,
        args
      }
    })

  }, [form?.sub_item, form?.item, attach_selected_content])

  const onKeyDown = React.useCallback((event, items) => {
    if (event.shiftKey) {
      return
    }

    const itemsCount = sub_menu_visible ? sub_menu_items?.length : items.length;

    if (event.key === 'Escape') {
      event.stopPropagation();
      event.preventDefault();

      handleClose();
    } else if (event.key === 'ArrowDown' && itemsCount > 0) {
      if (!sub_menu_visible) {
        setItemTargeted(prevState => {
          if (prevState < itemsCount - 1) {
            return prevState + 1;
          }

          return 0;
        })
      } else {
        set_sub_menu_item_targeted(prevState => {
          if (prevState < itemsCount - 1) {
            return prevState + 1;
          }

          return 0;
        })
      }

      event.stopPropagation();
      event.preventDefault();
    } else if (event.key === 'ArrowUp' && itemsCount > 0) {
      if (!sub_menu_visible) {
        setItemTargeted(prevState => {
          if (prevState > 0) {
            return prevState - 1;
          }

          return itemsCount - 1;
        })
      } else {
        set_sub_menu_item_targeted(prevState => {
          if (prevState > 0) {
            return prevState - 1;
          }

          return itemsCount - 1;
        })
      }

      event.stopPropagation();
      event.preventDefault();
    } else if (event.key === 'ArrowRight') {
      if (confirmMessage) {
        set_confirm_option_switcher(Math.random() + 1);
      } else {
        if (itemTargeted < 0) {
          return;
        }

        open_sub_menu(itemTargeted);
      }

      if (sub_menu_visible) {
        event.stopPropagation();
        event.preventDefault();
      }
    } else if (event.key === 'ArrowLeft') {
      if (confirmMessage) {
        set_confirm_option_switcher(Math.random() + 1);
      } else {
        if (sub_menu_anchor) {
          close_sub_menu()
        }
      }

      if (sub_menu_visible) {
        event.stopPropagation();
        event.preventDefault();
      }
    }
  }, [sub_menu_visible, itemTargeted, sub_menu_item_targeted, sub_menu_anchor, filteredAssistantItems, sub_menu_items, sub_menu_item_clicked, itemSelected, open_sub_menu])

  const onKeyPress = React.useCallback((event, items) => {
    const itemsCount = sub_menu_visible ? sub_menu_items?.length : items.length;

    if (!extendInput && event.key === 'Enter' && !event.shiftKey) {
      if (confirmMessage) {
        set_confirm_option_enter(Math.random() + 1);
      } else if (sub_menu_visible) {
        if (sub_menu_item_targeted < 0 || sub_menu_item_targeted >= itemsCount) {
          return;
        }

        sub_menu_item_clicked({ parent: sub_menu_parent, item: sub_menu_items[sub_menu_item_targeted] });
      } else {
        if (!open_sub_menu(itemTargeted)) {
          if ((itemTargeted < 0 || itemTargeted >= itemsCount)) {
            !form?.args?.find(arg => arg.type == 'text') && askAI();
            return;
          }

          itemSelected({ order: itemTargeted, callback: () => setItemTargeted(0) });
        }
        // setItemTargeted(0);
      }

      event.stopPropagation();
      event.preventDefault();
    }
  }, [extendInput, sub_menu_visible, itemTargeted, sub_menu_item_targeted, sub_menu_items, sub_menu_item_clicked, itemSelected, open_sub_menu])


  const close_sub_menu = () => {
    set_sub_menu_visible(false);
    set_sub_menu_anchor(null);
    set_sub_menu_items(null);
    set_sub_menu_parent(null)
  }

  const askAI = () => {
    // console.log('ask ai.................', searchText)
    if (searchText && !loading) {
      aiAction({
        action: generator === 'mindmap' && mindmap_application || 'query',
        userInput: textInputRef.current?.innerText || textInputRef.current?.textContent,
        aiResponse: generator === 'mindmap' ? undefined : aiResponse
      });
    } else if (form && !aiResponse) {
      const validArgs = form.args.filter(arg => !arg.disabled);
      const required_args = validArgs.filter(arg => arg.required);
      if (required_args?.length > 0) {
        for (const arg of required_args) {
          if (!arg.value?.trim()) {
            return showErrorMsg('missing_required_data');
          }
        }
      } else {
        if (!attach_selected_content && validArgs.length > 0 && !validArgs.find(arg => !!arg.value?.trim())) {
          return showErrorMsg('missing_one_data');
        }
      }

      aiAction({ action: form.item.action, sub_item: form.sub_item, aiResponse });
    } else if (!searchText) {
      setErrMsg(intl.formatMessage({ id: 'should_text_or_select_item' }))
    }
  }

  const [copied, setCopied] = React.useState();
  const copy_it = React.useCallback((text, cb) => {
    navigator.clipboard.writeText(text).then(() => {
      cb();
      selectionManager.setGeneratedText(text);
    });
  }, [selectionManager]);

  const [showSharePopover, setShowSharePopover] = React.useState(false);
  const [shareData, setShareData] = React.useState();
  const operationStatus = useSelector(state => state.operationStatus);

  React.useEffect(() => {
    if (!operationStatus?.inProgress) {
      setShareData(prevState => {
        return {
          ...prevState,
          uploading: false
        }
      });
    }
  }, [operationStatus])

  const toggleShare = (visibility) => {
    if (showSharePopover) {
      return setShowSharePopover(false);
    }

    if (shareData?.sessionId == doing.sessionId && shareData?.url) {
      return setShowSharePopover(true);
    }

    if (shareData?.sessionId !== doing.sessionId || !shareData?.url) {
      let data = {
        action: doing.actionItem,
        actionName: doing.label,
        userInput: doing.userInput,
        form: doing.form?.args?.find(arg => !!arg.value) ? doing.form : undefined,
        generated: aiResponse.content,
        visibility,
        sessionId: doing.sessionId
      }

      if (attach_selected_content) {
        data.selectedText = selectedText;
        data.title = selectedContent?.title;
        data.contextType = selectedContent?.type;
        data.excerpt = selectedContent?.excerpt
        data.url = (isRIL || selectedContent?.type == 'video') ? window.location.href : undefined;
      }

      setShareData({ sessionId: doing.sessionId, uploading: true });
      dispatch(shareAIContent({ data }, (url) => {
        setShareData(prevState => ({
          ...prevState,
          url: !loginUser?._id ? url : (url + '&shareId=' + loginUser._id.substring(loginUser._id.length - 8)),
          quoteText: aiResponse.content,
          uploading: false
        }))

        setShowSharePopover(true);
      }))
    }

  }

  const [loading, setLoading] = React.useState();
  const [subTasking, setSubTasking] = React.useState();
  const [cancelled, setCancelled] = React.useState();
  const [doing, setDoing] = React.useState();
  const [aiRespItem, setAiRespItem] = React.useState();
  const [aiResponse, setAiResponse] = React.useState();
  const [prevAIResponse, setPrevAIResponse] = React.useState();
  const [aiResultRefresher, setAiResultRefresher] = React.useState();

  const generateDone = (aiRespItem) => {
    setSubTasking(false);
    setLoading(false);
    setItemTargeted(-1);
    setUserInput('');

    setCopied(false);

    if (aiRespItem?.subTask) {
      setForm(form => {
        let args = cloneDeep(form.args);
        let arg = args.find(arg => arg.name == (aiRespItem.subTask.to_arg || aiRespItem.subTask.dynamic_arg?.name));
        if (arg) {
          arg.orignalValue = undefined;
        }


        return {
          ...form,
          args
        }
      })
    }
    // setForm(null);
  }

  const initContent = (aiRespItem) => {
    const { subTask } = aiRespItem;

    if (subTask) {
      setForm(form => {
        let args = cloneDeep(form.args);
        let arg = args.find(arg => arg.name == (subTask.to_arg || subTask.dynamic_arg?.name));
        if (arg) {
          arg.orignalValue = arg.value;
        }

        return {
          ...form,
          args
        }
      })
    }

    let content = aiRespItem.action !== 'continue' ? '' : (aiRespItem.menuItem?.id === 'retry' ? (prevAIResponse?.content || '') : (aiResponse?.content || '')) + '\n\n'
    return {
      ...aiRespItem,
      content
    }
  }

  React.useEffect(() => {
    if (!aiRespItem) {
      return;
    }

    if (aiRespItem.sessionId && cancelled?.sessionId === aiRespItem.sessionId) {
      return;
    }

    if (aiRespItem.err) {
      generateDone(aiRespItem);

      setAiResponse({
        ...aiRespItem
      })
    } else {
      let content = aiRespItem.content;
      if (!aiRespItem.stream) {
        content = initContent(aiRespItem)?.content + content;
      }

      if (aiRespItem.done || !aiRespItem.stream) {
        setPrevAIResponse(aiResponse);

        generateDone(aiRespItem);
      }

      if (doing?.subTask) {
        setForm(form => {
          let args = cloneDeep(form.args);
          let arg = args.find(arg => arg.name == (doing.subTask.to_arg || doing.subTask.dynamic_arg?.name));
          if (arg) {
            if (arg.readOnly) {
              arg.value = aiRespItem.content;
            } else {
              arg.value = (arg.orignalValue?.trim() ? arg.orignalValue.trim() + "\n" : "") + aiRespItem.content;
            }
          }

          return {
            ...form,
            args
          }
        })

        return;
      }

      const jsonData = extractJSONFromString(content);
      if (jsonData?.generated) {
        // console.log('jsonData..........', jsonData)
        dispatch({
          type: STRUCTURED_GENERATED_CONTENT,
          value: { content: jsonData.generated, ai_action: aiRespItem.action }
        })
      }

      setAiResponse({
        ...aiRespItem,
        content: jsonData?.generated && 'Mind map generated' || content
      });

      set_main_inner_max_height(280);
      setHeight();
    }
  }, [aiRespItem])

  React.useEffect(() => {
    if (contentContainerRef?.current) {
      contentContainerRef.current.scrollTop = contentContainerRef.current.scrollHeight
    }
  }, [contentContainerRef?.current?.scrollHeight])

  const resizableContainerRef = React.useRef();

  React.useEffect(() => {
    const handleMouseMove = (e) => {
      if (!isResizing) return;

      setWidth(resizableContainerRef?.current?.offsetWidth + 10);
      set_main_inner_max_height(resizableContainerRef?.current?.height)
    };

    const handleMouseUp = (e) => {
      if (!isResizing) {
        handleClose(false, true)
      }

      setIsResizing(false);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing, aiResponse, handleClose]);

  React.useEffect(() => {
    if (!aiResultRefresher) return;

    if (cancelled && cancelled.sessionId === aiResultRefresher.sessionId) {
      return;
    }

    dispatch(refreshAIResponse({ sessionId: aiResultRefresher.sessionId, isChat: false }, (item) => {
      if (item) {
        setAiRespItem({ ...item, menuItem: aiResultRefresher.menuItem });
      } else {
        if (new Date().getTime() - aiResultRefresher.sessionId > 110 * 1000) {
          setLoading(false);
          showErrorMsg('ai_timeout');
          return;
        }

        setTimeout(() => {
          setAiResultRefresher({ ...aiResultRefresher, refreshId: Math.random() })
        }, 5 * 1000)
      }
    }, 'aimodal'))
  }, [aiResultRefresher])

  const showErrorMsg = (err) => {
    setLoading(false);
    setSubTasking(false);
    setErrMsgId(err);
    // if (subTask) {
    //   setSubTaskErrMsg(intl.formatMessage({ id: err }));
    // } else {
    setErrMsg(intl.formatMessage({ id: err }));
    // }
  }

  const isChinese = React.useCallback((str) => {
    var reg = /[\u4e00-\u9fa5]/g; //使用Unicode字符集范围来匹配中文字符
    return reg.test(str);
  }, [])

  const llmConfiguredInvalid = React.useMemo(() => {
    return !using_model || using_model.level !== 'private' && !loginUser?._id || using_model.level === 'private' && !(using_model.token && using_model.model)
    // return !aiSettings?.usingAPI && !loginUser?._id
    //   || aiSettings?.usingAPI && (!aiProvider || !aiSettings[aiProvider]?.token || !aiSettings[aiProvider]?.model
    //     || aiProvider != 'gemini' && nodeType === 'image'
    //   )
  }, [loginUser, using_model])

  const aiAction = ({ menuItem, action, userInput = '', sub_item, subTask, aiResponse, disableContext }) => {
    setAiRespItem(null);

    // console.log('ai action.........', { menuItem, action, text, sub_item, subTask, searchText, disableContext })
    if (llmConfiguredInvalid) {
      return setConfirmMessage({
        content: intl.formatMessage({
          id:
            // aiSettings?.usingAPI && aiProvider != 'gemini' && selectedContent?.type === 'image' ? 'invalid_api_for_image' : 
            'invalid_api_settings'
        }),
        okText: intl.formatMessage({ id: 'settings' }),
        onConfirm: () => {
          openSettings();
          setConfirmMessage({
            content: intl.formatMessage({ id: 'confirm_api_set' }),
            onConfirm: async () => {
              dispatch(getUserInfo({}))
              await refresh()
              setConfirmMessage(null)
            }
          })
        }
      })
    }

    // text = text?.trim();
    const sessionId = new Date().getTime();

    let actionItem = get_ai_prompt_item(action);

    if (!userInput?.trim() && !selectedText?.trim() && selectedContent?.type != 'image' && !aiResponse?.content && action != 'xSlides' && !form) {
      return showErrorMsg('no_text')
    } else if (objType != 'ril' && using_model?.level != 'private' && (selectedText?.length > TEXT_LEN_LIMIT || userInput?.length > TEXT_LEN_LIMIT || aiResponse?.content?.length > TEXT_LEN_LIMIT)) {
      return showErrorMsg('text_too_long')
    } else {
      setErrMsg(null);
      setErrMsgId(null);
    }

    let lng = lang && lng_list?.find(l => l.Symbol == lang);

    let newDoing = { action, sessionId, sub_item, form, subTask, userInput, aiResponse, disableContext, actionItem: doing?.actionItem || actionItem };
    if (actionItem) {
      newDoing.label = actionItem.label;
    } else {
      newDoing.label = doing?.label;
    }

    setDoing(newDoing);

    if (subTask) {
      setSubTasking(subTask.action);
    } else {
      setLoading(true);
    }

    if (!actionItem) {
      actionItem = {
        action
      }

      if (generator === 'mindmap') {
        actionItem.objTypes = ['flow'];
      }
    }

    actionItem = { ...actionItem };
    if (actionItem.action === 'translate_image') {
      actionItem.prompt = actionItem.prompt.replace('{{Target Language}}', lng.Language)
    }

    let content = userInput;
    if (aiResponse) {
      //has aiResonpse means action after AI response, otherwise means fresh task for AI.
      if (userInput) {
        content = '[Given text]:```' + aiResponse.content + '```\n\n[user]: ' + userInput;
      } else {
        content = aiResponse.content;
      }
    } else {
      if (actionItem.prompt_user) {
        content = actionItem.prompt_user
      }
      if (form) {
        if (subTask) {
          if (subTask.dynamic_arg && !form.args.find(arg => arg.name == subTask.dynamic_arg.name)) {
            setForm(prevState => {
              let args = [...prevState.args];
              args.unshift(subTask.dynamic_arg)

              return {
                ...prevState,
                args
              }
            })
          }
          content = subTask.prompt_user;
        } else {
          content = form.sub_item?.prompt_user || form.item?.prompt_user || content;
        }

        if (form.sub_item) {
          content = content.replaceAll('{{sub_item}}', form.sub_item.label);
        }

        let unused_args = [];
        for (let arg of form.args) {
          if (!arg.disabled && !arg.readOnly) {
            if (content.indexOf(`{{${arg.name}}}`) > -1) {
              content = content.replaceAll(`{{${arg.name}}}`, '```' + (arg.value || intl.formatMessage({ id: 'not_provided' })) + '```');
            } else if (arg.value) {
              unused_args.push(arg)
            }
          }
        }

        if (unused_args.length > 0) {
          content = content + '\n\nGiven context or requirements:\n' + unused_args.map(arg => {
            return arg.label + ': ```' + arg.value + '```';
          }).join('\n');
        }
      }

      // console.log('prepare content........', { content, selectedText, selected_content_chooseable, attach_selected_content })
      if (selectedText?.trim()) {
        if (!content) {
          content = selectedText;
        } else if (content.includes('{{selected_text}}')) {
          content = content.replaceAll('{{selected_text}}', '```' + selectedText + '```');
        } else if (attach_selected_content) {
          content = intl.formatMessage({ id: 'context' }) + ': ' + '```' + selectedText + '```.' + '\n' + content
        }
      }

      // console.log('content with selected content..........', { content, selected_content_attachable, attach_selected_content, initText })
      let contextText;

      if (!disableContext && context) {
        if (context.type == 'task_content_from_selected') {
          contextText = selectedText;
        } else if (context.type == 'choose_a_doc' && context.value) {
          contextText = context.value.type == 'doc' && ('# ' + context.value.title + '\n\n' + extractContextFromBlocks(context.value.blocks))
            || context.value.type == 'slides' && context.value.markdown;
        } else if (context.type == 'current_doc') {
          contextText = dialogState.caller === 'plate' && ('# ' + docs.byId[hid]?.title + '\n\n' + extractContextFromBlocks(docs.byId[hid]?.blocks))
            || dialogState.caller === 'slides' && dialogState.pageContent;
        }

        if (contextText) {
          content = content + '.\n' + intl.formatMessage({ id: 'context' }) + ': ' + '```' + contextText + '```.'
        }
      }

      if (disableContext) {
        setContext(null);
      }
    }

    if (generator === 'mindmap' && showMentalModel && mentalModel && mentalModel.value != 'none') {
      content = content + '\n\n' + '[Specified mental model and analysis framework]: ' + (mentalModel.inputValue || mentalModel.label);
    }

    // content = actionItem.group === 9 ? actionItem.prompt.replace('[TEXT]', `"${text}"`) : (form ? drafting.prompt_user + text : text);
    // if (action === 'query' && filledSelection || actionItem.type === 'user_installed' && (filledSelection || actionItem.content_source === 'selected')) {
    // if (contextText || actionItem.content_source === 'selected') {
    //   content += '\n\nThe given text is in Markdown format, please also reply in Markdown format'
    // }

    if (using_model?.level === 'private') {
      // console.log('prompts..........', action, actionItem, prompt)
      let messages = content ? [{
        role: 'user',
        content,
      }] : [];

      let prompt = actionItem.type != 'user_installed' && actionItem.prompt;
      if (actionItem.type != 'user_installed' && actionItem.sub_items && actionItem.prompts) {
        prompt = actionItem.prompts[actionItem.sub_items.findIndex(item => item.value === sub_item?.value)];
      }

      if (prompt && !subTask) {
        prompt += '. Reply with markdown format';

        messages.unshift({
          role: 'system',
          content: prompt
        });
      }

      if (!action?.includes('translate')) {
        if (lng && lng.Symbol != 'as_context') {
          messages[0].content = messages[0].content + `. Respond in ${lng.Language}`;
        } else {
          messages[0].content = messages[0].content + `. If no language is specified, reply in the same language as the given text or context`;
        }
      }

      let params = {
        model: using_model.model
      }

      if (['openai', 'groq', 'openai_compatible'].includes(using_model.provider)) {
        if (selectedContent?.image) {
          let user_message = messages.find(msg => msg.role == 'user');
          if (user_message) {
            user_message.content = [{
              type: 'text',
              text: user_message.content
            }, {
              type: 'image_url',
              image_url: {
                url: `data:image/jpeg;base64,${selectedContent.image}`
              }
            }]
          } else {
            messages.push({
              role: 'user',
              content: [{
                type: 'image_url',
                image_url: {
                  url: `data:image/jpeg;base64,${selectedContent.image}`
                }
              }]
            })
          }
        }

        params.messages = messages;
        if (actionItem?.temperature) {
          params.temperature = actionItem?.temperature;
        }
      } else if (using_model.provider == 'gemini') {
        let human_content = messages.length == 1 ? messages[0].content : ((!form ? 'The given text is:' : '') + messages[1].content + '\n\n' + messages[0].content);
        // console.log('human content:', human_content)
        params.contents = [{
          parts: [{ text: human_content }]
        }];

        if (selectedContent?.type == 'image') {
          params.contents[0].parts.push({
            "inline_data": {
              "mime_type": "image/jpeg",
              "data": removeDataURLPrefix(selectedContent.image)
            }
          })
        }
        params.generationConfig = {
          temperature: actionItem?.temperature
        }
      } else if (using_model.provider == 'anthropic') {
        params.max_tokens = 3000;

        let system = messages.find(msg => msg.role == 'system');
        if (system) {
          params.system = system.content
        }
        params.messages = messages.filter(msg => msg.role != 'system');
      }

      // console.log('action...........', action, params)
      // console.log('messages to open ai......', actionItem, prompt, messages)
      if (['openai', 'groq', 'gemini', 'openai_compatible'].includes(using_model.provider)) {
        let streamGenerator = openAIStreamGenerate;
        if (using_model.provider == 'gemini') {
          streamGenerator = geminiStreamGenerate;
        }

        streamGenerator(using_model.endpoint, using_model.token, params, () => {
          setAiRespItem(initContent({
            sessionId,
            action,
            menuItem,
            subTask,
            stream: true
          }))
        }, (chunkText) => {
          setAiRespItem(prevState => {
            return {
              ...prevState,
              content: (prevState?.content || "") + chunkText
            }
          })
        }, (errMsg) => {
          setAiRespItem(prevState => {
            return {
              ...prevState,
              err: errMsg
            }
          })
        }, () => {
          setAiRespItem(prevState => {
            return {
              ...prevState,
              done: true
            }
          })
        })
      } else {
        dispatch(AI_API_CALLER[using_model.provider](using_model.endpoint, using_model.token, params, (item) => {
          setAiRespItem({
            content: removeQuotes(item),
            sessionId,
            action,
            menuItem,
            subTask
          });
        }, (errMsg) => {
          setLoading(false);
          setSubTasking(false);
          setErrMsg("Failed, reason: " + intl.formatMessage({ id: errMsg }));
          setAiRespItem(prevState => {
            return {
              ...prevState,
              err: errMsg
            }
          })
        }))
      }
    } else {
      // console.log('action_item..........', actionItem, objType, lng)
      dispatch(callAIAssist({
        data: {
          model: selectedContent?.image && using_model?.level === 't2' && 'gemini-1.5-flash-latest' || using_model?.value,
          model_level: using_model?.level,
          action: actionItem.type === 'user_installed' || subTask ? 'query' : action,
          sub_item: sub_item?.value,
          use_search: ai_use_search && extendInput, //extendInput means user prompt
          // objType: actionItem.action == 'draft' && objType == 'ril' ? actionItem.objTypes[0] : objType,
          objType: actionItem.objTypes?.includes(objType) && objType || actionItem.objTypes?.length == 1 && actionItem.objTypes[0] || 'markdown',
          lang: lng && lng.Symbol != 'as_context' ? lng.Language : undefined,
          content,
          image: selectedContent?.image,
          hid: dialogState.hid,
          sessionId,
          app: 'funblocks_ext',
          service: generator === 'mindmap' ? 'flow' : undefined
        }
      }, (item) => {
        setAiRespItem({ ...item, menuItem, subTask });
      }, (err) => {
        if (err === 'TIMEOUT' && (!cancelled || cancelled.sessionId != sessionId)) {
          setTimeout(() => {
            // refreshAIResp(sessionId, menuItem, action);
            setAiResultRefresher({ sessionId, menuItem, action, refreshId: Math.random() })
          }, 3 * 1000)
        } else {
          setLoading(false);
          setSubTasking(false);
        }

        setAiRespItem(prevState => {
          return {
            ...prevState,
            err
          }
        })
        // setAiRespItem({
        //   err
        // })
      }))
    }
  }

  const retry = () => {
    if (!doing) return;

    setErrMsg(null);
    if (doing.action === 'query') {
      setAiResponse(null);
      setUserInput(doing.userInput);
      return;
    } else if (doing.form) {
      setAiResponse(null);
      setForm(doing.form);
      return;
    }

    aiAction({ ...doing, menuItem: { id: 'retry' } });
  }

  const memoriable = React.useCallback(async (attach_selected_content, doing, selection, ai_generated) => {
    const selectedContent = selection?.getSelectedContent();
    const isVideo = selectedContent?.type == 'video';
    const isEditable = selectedContent?.isEditable;

    let quoted_text = '';
    if (attach_selected_content && selectedContent?.text && !isRIL && !isVideo) {
      quoted_text = intl.formatMessage({ id: 'ask_ai_about_selected' }) + ': ' + selectedContent?.text
    }

    let user_input_text;
    if (doing.form) {
      user_input_text = doing.form.args?.map(arg => {
        return arg.label + ': ' + (arg.value || intl.formatMessage({ id: 'not_provided' }))
      }).join('\n');
    } else if (doing.userInput) {
      user_input_text = intl.formatMessage({ id: 'my_prompt' }) + ': ' + doing.userInput
    }

    if (quoted_text && user_input_text) {
      quoted_text += '\n' + user_input_text;
    } else {
      quoted_text += (user_input_text || '');
    }

    let title;

    let readObj = {}
    if (!isEditable && !isGoogleDocs && attach_selected_content) {
      title = selectedContent?.title;
      readObj.url = window.location.href;
      if (isVideo) {
        readObj.subtitles = selectedContent?.subtitles;
      } else if (isRIL) {
        let article = await extractWebContent();
        if (article) {
          readObj.article_content = article.content;
        }
      }
    }

    if (!title) {
      title = ai_generated?.trim().split('\n')[0] || quoted_text?.trim().split('\n')[0];
    }

    let obj = {
      quoted_text: quoted_text?.trim(),
      action_info: doing.label ? `${doing.label} (by @FunBlocks AI)` : 'by @FunBlocks AI',
      ai_generated: ai_generated?.trim(),
      title,
      readObj
    }
    obj.share_content = obj.ai_generated + '\n' + obj.action_info + '\n' + obj.quoted_text

    return obj;
  }, [isRIL])

  const saveToMemo = React.useCallback(async (attach_selected_content, doing, selection, ai_generated, successCallback) => {
    const memoObj = await memoriable(attach_selected_content, doing, selection, ai_generated);

    let blocks = !memoObj.quoted_text ? [] : [{
      type: 'blockquote',
      children: memoObj.quoted_text.split('\n').map(txt => {
        return {
          type: 'p',
          children: [{
            text: txt
          }]
        }
      })
    }];

    blocks.push({
      type: 'h3',
      children: [{
        text: memoObj.action_info,
        color: 'gray'
      }]
    })

    blocks = blocks.concat(memoObj.ai_generated.split('\n').map(txt => {
      return {
        type: 'p',
        children: [{
          text: txt
        }]
      }
    }));

    let data = {
      title: memoObj.title,
      content: blocks,
      ...memoObj.readObj
    };

    dispatch(addNote({
      data
    }, successCallback, 'widget'));
  }, [])

  const handleSaveToMemo = async (callback) => {
    if (!loginUser?._id) {
      return setConfirmMessage({
        content: intl.formatMessage({ id: 'feature_for_members' }),
        okText: intl.formatMessage({ id: 'register_now' }),
        onConfirm: () => {
          openSettings();
          setConfirmMessage({
            content: intl.formatMessage({ id: 'confirm_logon' }),
            onConfirm: () => dispatch(getUserInfo({}, () => setConfirmMessage(null), () => handleSaveToMemo(callback)))
          })
        }
      })
    }
    await saveToMemo(attach_selected_content, doing, selectionManager, mdContainerRef.current.innerText, () => {
      callback()
      textInputRef?.current?.focus();
      setDoneMessage({
        content: intl.formatMessage({ id: 'saved_to_memo' })
      });
      setTimeout(() => setDoneMessage(null), 1500);
    });
  }

  const hasEditingActions = selectedContent?.isEditable;
  const editOption = selectionManager.getEditOption();
  const responseMenuItems = React.useMemo(() => [{
    id: 'replace',
    label: intl.formatMessage({ id: 'replace_selection' }),
    icon: <Check size={18} color='#777' />,
    action: () => {
      copy_it(mdContainerRef.current.innerText, () => {
        selectionManager.replace(mdContainerRef.current.innerText);
        setDoneMessage({
          content: intl.formatMessage({ id: 'copied_also' })
        });
        setTimeout(() => hide(), 1500);
      })
    },
    group: 11,
  }, {
    id: 'close',
    label: intl.formatMessage({ id: 'discard' }),
    icon: <Close size={18} color='#777' />,
    action: () => handleClose(),
    group: 11,
  }, {
    id: 'insertBelow',
    label: intl.formatMessage({ id: 'insert_below' }),
    icon: <ArrowToBottom size={18} color='#777' />,
    action: () => {
      copy_it(mdContainerRef.current.innerText, () => {
        selectionManager.append(mdContainerRef.current.innerText);
        setDoneMessage({
          content: intl.formatMessage({ id: 'copied_also' })
        });
        setTimeout(() => hide(), 1500);
      })
    },
    group: 11,
  }, {
    id: 'retry',
    label: intl.formatMessage({ id: 'try_again' }),
    icon: <Refresh size={17} color='#777' />,
    action: () => retry(),
    group: 11,
  }, {
    id: 'continue',
    action: 'continue',
    icon: <Wand size={18} color='#777' />,
    group: 12,
  }, {
    id: 'extend',
    icon: <Expand size={18} color='#777' />,
    action: 'extend',
    group: 12,
  }, {
    id: 'translate',
    action: 'translate',
    icon: <Translate size={18} color='#777' />,
    group: 12,
  }, {
    id: 'copy',
    label: intl.formatMessage({ id: copied ? 'copied' : 'copy_generated_content' }),
    icon: copied ? <Check size={18} color='green' /> : <ContentCopy size={18} color='#777' />,
    action: () => {
      copy_it(mdContainerRef.current.innerText, () => {
        setCopied(true);
        setTimeout(() => setCopied(false), 3000)
        // setDoneMessage({
        //   content: intl.formatMessage({ id: 'copied' })
        // });
      })
    },
    group: 13,
  }, {
    id: 'save_to_memo',
    label: intl.formatMessage({ id: 'save_to_memo' }),
    icon: <Save size={18} color='#777' />,
    action: (callback) => handleSaveToMemo(callback),
    group: 13,
  }, {
    id: 'back',
    label: intl.formatMessage({ id: 'back' }),
    icon: <ArrowBack size={17} color='#777' />,
    action: () => handleBack(),
    group: 13,
  }].map(item => {
    let existing_item = assistant_items?.find(i => i.action === item.action);
    if (!existing_item) {
      return item;
    }

    return {
      ...existing_item,
      ...item
    }
  }), [intl, doing, copied, aiResponse?.content, assistant_items, handleBack, handleClose, retry, selectionManager, hasEditingActions]);

  const groupMenuItems = (items) => {
    let grouped = Array.from({ length: Math.max(...items.map(item => item.group)) + 1 }, () => []);

    items.forEach(item => {
      if (typeof item.group == 'number') {
        grouped[isGoogleDocs ? 20 - item.group : item.group]?.push({ ...item });
      }
    });

    let index = 0;
    grouped.forEach(group => {
      group.forEach(item => {
        item.order = index;
        index++;
      })
    })

    grouped = grouped.flatMap((group, i) => {
      if (!group || group.length === 0) {
        return [];
      }

      const item_group = assistant_items_groups?.find(item => item.group === i);

      if (item_group) {
        group.unshift({
          type: 'groupName',
          label: item_group.label,
          group: i
        })
      }

      if (i > 0) {
        group.unshift({
          type: 'divider'
        })
      }

      return group;
    });

    return grouped;
  }

  React.useEffect(() => {
    if (!aiResponse && !extendedAssistantItems) return;

    let itemsFiltered;
    if (loading) {
      if (['continue', 'optimize'].includes(init_action)) {
        itemsFiltered = responseMenuItems.filter(item => ['close'].includes(item.id));
      } else {
        itemsFiltered = responseMenuItems.filter(item => ['back', 'close'].includes(item.id));
      }
    } else if (!aiResponse) {
      itemsFiltered = extendedAssistantItems;

      if (form) {
        itemsFiltered = responseMenuItems.filter(item => ['back', 'close'].includes(item.id));
      } else if (['continue', 'optimize'].includes(init_action)) {
        itemsFiltered = [];
      }
    } else {
      itemsFiltered = responseMenuItems;

      if (aiResponse.err) {
        itemsFiltered = itemsFiltered.filter(item => !['copy', 'save_to_memo', 'continue', 'extend', 'insertBelow', 'replace', 'translate'].includes(item.id));
      }

      if (!hasEditingActions || editOption == 'insert_only') {
        itemsFiltered = itemsFiltered.filter(item => !['replace'].includes(item.id));
      }

      if (!hasEditingActions || editOption == 'replace_only' || init_action === 'reply_email') {
        itemsFiltered = itemsFiltered.filter(item => !['insertBelow'].includes(item.id));
      }
    }

    itemsFiltered = itemsFiltered.filter(item => {
      // return  ['back', 'close'].includes(item.id) || 
      return item.label?.toLowerCase().includes(searchText.toLowerCase()) ||
        typeof item.action === 'string' && item.type !== 'user_installed' && item.action.toLowerCase().includes(searchText.toLowerCase());
    });

    const replaceItem = itemsFiltered.find(item => item.id === 'replace');
    if (!replaceItem) {
      const insertBelowIndex = itemsFiltered.findIndex(item => item.id === 'insertBelow');

      if (insertBelowIndex !== -1) {
        const [insertBelowItem] = itemsFiltered.splice(insertBelowIndex, 1);
        itemsFiltered.unshift(insertBelowItem);
      }
    }

    setFilteredAssistantItems(itemsFiltered);

    let itemSelected = 0;
    if (!aiResponse && itemsFiltered.find(item => item.id === 'back')
      || aiResponse && itemsFiltered.length == 2
      || itemsFiltered.length == 0) {
      itemSelected = -1;
    }

    !copied && setItemTargeted(itemSelected);
  }, [searchText, extendedAssistantItems, aiResponse, loading, form, using_model, lang, copied]);

  const selected_content_chooseable = React.useMemo(() => {
    const itemsCount = groupedAssistantItems.length;
    if (searchText && (itemTargeted < 0 || itemTargeted >= itemsCount)) {
      return true;
    }

    const item = init_action ? assistant_items?.find(i => i.action == init_action) : groupedAssistantItems?.find(item => item.order === itemTargeted);

    if (item?.prompt?.includes('{{selected_text}}') || item?.prompt_user?.includes('{{selected_text}}')
      || form?.item?.prompt_user?.includes('{{selected_text}}') || form?.sub_item?.prompt_user?.includes('{{selected_text}}')) {
      return false
    }

    if (form) {
      return true;
    }

    return item?.type !== 'user_installed' && item?.group == 3 || item?.type == 'user_installed' && !item?.prompt_user?.includes('{{selected_text}}')
  }, [itemTargeted, groupedAssistantItems, form])

  React.useEffect(() => {
    if (!filteredAssistantItems) return;

    setGroupedAssistantItems(groupMenuItems(filteredAssistantItems));
  }, [filteredAssistantItems])

  React.useLayoutEffect(() => {
    if (!textInputRef?.current) return;

    setTimeout(() => textInputRef.current?.focus(), 200);
  }, [textInputRef?.current])

  React.useLayoutEffect(() => {
    if (!form_input_ref?.current) return;
    setTimeout(() => form_input_ref?.current?.focus(), 200);
  }, [form_input_ref?.current])

  const openSettings = () => {
    browser.runtime.sendMessage({
      type: EventName.showOptions,
    });
  }

  const gotoPrompts = () => {
    // dispatch({ type: SETTINGS_DIALOG, value: { visible: true, page: 'prompts' } });
    window.open(`http://app.${funblocks_domain}/#/embed/editor?hid=funblocks_agents`)
  }

  const renderSubMenu = React.useCallback((sub_items) => {
    return <div
      style={{ 
        width: 200, 
        backgroundColor: 'white',
        boxShadow: '0px 0px 8px #bbb',
        borderRadius: '6px'
      }}
      onMouseDown={(e) => {
        e.stopPropagation();
        e.preventDefault();
      }}
      onMouseUp={(e) => {
        e.stopPropagation();
        e.preventDefault();
      }}
    > {sub_items?.map((sub_item, i) => {
      return <div
        style={{ fontSize: 13 }}
        ref={(ref) => {
          sub_menu_item_refs.current = { ...sub_menu_item_refs.current, [i]: ref };
        }}
        key={i + ''}
        onMouseEnter={() => {
          set_sub_menu_item_targeted(i)
        }}
      >
        <div
          key={i + ''}
          onClick={(e) => {
            sub_menu_item_clicked({ parent: sub_menu_parent, item: sub_item });
            e.stopPropagation();
            e.preventDefault();
          }}
          className='hoverStand'
          style={{ backgroundColor: i === sub_menu_item_targeted ? 'rgb(212,228,247)' : undefined, flexDirection: 'row', justifyContent: 'space-between', cursor: 'pointer', padding: '5px', paddingLeft: '12px' }}
        >
          {sub_item.label}
          {i === sub_menu_item_targeted && <KeyboardReturn size={20} style={{ color: 'gray', paddingLeft: 6 }} />}
        </div>
      </div>
    })}
    </div>
  }, [sub_menu_item_refs, sub_menu_item_clicked])

  const subMenu = React.useMemo(() => renderSubMenu(sub_menu_items, sub_menu_item_targeted), [sub_menu_items, sub_menu_item_targeted])

  // console.log('popoverAnchorEl..........', popoverAnchorEl)
  const renderMenuItem = React.useCallback((item, index, items) => {
    let menuItem = (<div
      key={index + ''}
      onClick={(e) => {
        itemSelected({ order: item.order });
        e.stopPropagation();
        e.preventDefault();
      }}
      className='hoverStand'
      style={{
        backgroundColor: item.order === itemTargeted ? 'rgb(212,228,247)' : undefined,
        padding: '5px', paddingLeft: '12px',
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between'
      }}
    >
      <div style={{
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        display: 'block',
        columnGap: 9,
        display: 'flex',
        flexDirection: 'row'
      }}>
        {!!item.icon && item.icon}
        {item.label}
      </div>
      {item.order === itemTargeted && <KeyboardReturn size={17} style={{ cursor: 'pointer', color: 'gray' }} />}
    </div>);

    // console.log('item subs..............', item.sub_items)
    // if (item.sub_items?.length) {
    //   const handlePopoverClick = (event) => {
    //     console.log('popoverAnchorEl setting..........0', event.currentTarget)
    //     if (sub_menu_visible && popoverAnchorEl) {
    //       close_sub_menu();
    //       setPopoverAnchorEl(null);
    //     } else {
    //       console.log('popoverAnchorEl setting..........', event.currentTarget)
    //       setPopoverAnchorEl(event.currentTarget);
    //       // 调用原有的菜单打开逻辑
    //       if (item.onClick) {
    //         item.onClick();
    //       }
    //     }
    //   };

    //   const handlePopoverClose = (event, reason) => {
    //     if (reason === 'backdropClick' || reason === 'escapeKeyDown') {
    //       setPopoverAnchorEl(null);
    //       close_sub_menu();
    //     }
    //   };

    //   menuItem = (
    //     <div>
    //       <div 
    //         onClick={handlePopoverClick}
    //       >
    //         {menuItem}
    //       </div>
    //       <Popover
    //         open={sub_menu_visible}
    //         anchorEl={popoverAnchorEl}
    //         onClose={handlePopoverClose}
    //         anchorOrigin={{
    //           vertical: 'center',
    //           horizontal: 'right',
    //         }}
    //         transformOrigin={{
    //           vertical: 'center',
    //           horizontal: 'left',
    //         }}
    //         disablePortal={false}
    //         container={() => document.body}
    //         style={{ zIndex: 2147483647 }}
    //         slotProps={{
    //           paper: {
    //             style: {
    //               padding: 0,
    //               width: 'fit-content',
    //               zIndex: 2147483647,
    //             }
    //           }
    //         }}
    //       >
    //         {subMenu}
    //       </Popover>
    //     </div>
    //   )
    // }

    return <div
      ref={(ref) => {
        elmRefs.current = { ...elmRefs.current, [item.order]: ref };
      }}
      key={index + ''}
    >
      {
        objType != 'image' && item.type === 'groupName' &&
        <div style={{ fontSize: 13, color: 'gray', padding: '6px', display: 'flex', flexDirection: 'row' }}>
          {item.label}
          {
            item.type === 'user_installed' && //TODO: bug heere
            <div style={{
              paddingLeft: '6px', paddingRight: '6px', cursor: 'pointer',
              display: 'flex', justifyContent: 'flex-end',
              flex: 1,
              whiteSpace: 'nowrap',
              // color: 'dodgerblue'
            }} onClick={() => gotoPrompts()}>{intl.formatMessage({ id: 'CRUD' })}</div>
          }
        </div>
      }
      {
        objType != 'image' && item.type === 'divider' && !!index && items.filter(it => it.type != 'divider').length > 2 &&
        <div
          className='fill-available'
          style={{ marginLeft: 5, marginRight: 10, height: 1, backgroundColor: '#ddd' }}
        />
      }
      {
        !['groupName', 'divider'].includes(item.type) &&
        menuItem
      }
    </div>
  }, [itemTargeted, intl, elmRefs, sub_menu_visible, sub_menu_parent, subMenu, itemSelected])

  const renderSubTaskButton = (subTask) => {
    return <>
      {
        subTasking != subTask.action &&
        <div style={{ display: 'flex', flexDirection: 'column' }}>
          <div
            className='hoverButton_transparent'
            style={{
              paddingLeft: '6px', paddingRight: '6px',
              paddingTop: '3px', paddingBottom: '3px',
              borderRadius: '6px',
              fontSize: '14px',
              cursor: 'pointer', whiteSpace: 'nowrap', display: 'flex', alignItems: 'center', flexDirection: 'row',
              backgroundColor: subTasking ? '#ddd' : undefined,
              color: subTasking ? 'white' : undefined,
              // border: subTasking ? undefined : 'solid 1px dodgerblue'
            }}
            onClick={() => {
              if (subTasking) return;
              const validArgs = form.args.filter(arg => !arg.disabled);
              const required_args = validArgs?.filter(arg => arg.required);
              if (required_args?.length > 0) {
                for (const arg of required_args) {
                  if (!arg.value?.trim()) {
                    return showErrorMsg('missing_required_data');
                  }
                }
              } else {
                if (!attach_selected_content && validArgs.length > 0 && !validArgs?.find(arg => !!arg.value?.trim())) {
                  return showErrorMsg('missing_one_data');
                }
              }

              aiAction({ action: form.item?.action, subTask, aiResponse })
            }}>
            {subTask.label}
            <div style={{ marginLeft: '6px', alignItems: 'center', display: 'flex' }} >
              <Magic size={16} />
            </div>
          </div>
        </div>
      }
      {
        subTasking == subTask.action && <>
          <ThreeDots
            height="8"
            width="30"
            radius="9"
            color='dodgerblue'
            ariaLabel='three-dots-loading'
          />
          <span style={{ marginLeft: '8px', marginRight: '8px', fontSize: '14px', color: 'gray', whiteSpace: 'nowrap' }}>
            {intl.formatMessage({ id: 'askAI_doing' })}
          </span>
        </>
      }
    </>
  }

  const askAI_btn = React.useMemo(() => {
    const button = <div
      className='hover_opacity'
      style={{
        paddingTop: '4px',
        paddingBottom: '4px',
        paddingLeft: '10px',
        paddingRight: '10px',
        color: 'white', cursor: 'pointer',
        backgroundColor: subTasking ? '#ddd' : 'dodgerblue',
        borderRadius: '14px',
        whiteSpace: 'nowrap',
        fontSize: '14px'
      }} onClick={() => !subTasking && askAI()}
    >
      {intl.formatMessage({ id: 'sendAI' })}
    </div>;

    if (form) {
      return button;
    }

    return <Tooltip
      title={intl.formatMessage({ id: 'askAI_directly_tooltip' })}
    >
      {button}
    </Tooltip>
  }, [intl, subTasking, askAI, form]);


  // const extendInput = React.useMemo(() => !selectedContent || !isInputZh && filteredAssistantItems?.length == 0, [aiResponse, filteredAssistantItems, isInputZh]);\
  React.useEffect(() => {
    setExtendInput(prevState => generator === 'mindmap' || prevState && !filteredAssistantItems?.length || !isInputZh && filteredAssistantItems?.length == 0)
  }, [filteredAssistantItems, isInputZh])

  const mindmap_applications = React.useMemo(() => {
    return [
      {
        label: intl.formatMessage({ id: 'mindmap_application_askai' }),
        value: 'flow_mindmap_askai'
      },
      {
        label: intl.formatMessage({ id: 'mindmap_application_brainstorming' }),
        value: 'flow_brainstorming'
      },
      {
        label: intl.formatMessage({ id: 'mindmap_application_critical_analysis' }),
        value: 'flow_critical_analysis'
      },
      {
        label: intl.formatMessage({ id: 'mindmap_application_decision_analysis' }),
        value: 'flow_decision_analysis'
      },
      {
        label: intl.formatMessage({ id: 'mindmap_application_breakdown' }),
        value: 'breakdown'
      },
      {
        label: intl.formatMessage({ id: 'mindmap_application_mindmap' }),
        value: 'flow_mindmap'
      }      
    ]
  }, [intl])

  const [mindmap_application, set_mindmap_application] = React.useState('flow_mindmap_askai');

  const showMentalModel = React.useMemo(() => {
    return ['flow_brainstorming', 'flow_decision_analysis'].includes(mindmap_application);
  }, [mindmap_application])

  const placeholder = React.useMemo(() => {
    if (userInput) {
      return null;
    }

    if (generator !== 'mindmap') {
      return intl.formatMessage({ id: aiResponse ? 'askAI_next' : 'askAI' });
    }

    if (mindmap_application === 'flow_mindmap_askai') {
      return intl.formatMessage({ id: 'mindmap_topic_placeholder_askai' });
    }

    if (mindmap_application === 'flow_mindmap') {
      return intl.formatMessage({ id: 'mindmap_topic_placeholder_mindmap' });
    }

    if (mindmap_application === 'flow_critical_analysis') {
      return intl.formatMessage({ id: 'mindmap_topic_placeholder_critical_analysis' });
    }

    if (mindmap_application === 'flow_decision_analysis') {
      return intl.formatMessage({ id: 'mindmap_topic_placeholder_decision_analysis' });
    }

    return intl.formatMessage({ id: 'mindmap_topic_placeholder' });
  }, [intl, userInput, aiResponse, generator]);

  const ai_magic_icon_and_status = React.useMemo(() => (
    <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
      <div
        className="text-lg handle animate__animated animate__fadeInDown"
        style={{ color: 'dodgerblue', marginRight: '6px', cursor: 'move', display: 'flex', flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}
        onMouseUp={() => {
          textInputRef?.current?.focus();
        }}
      >
        <Magic size={20} />
      </div>
      {
        (loading || extendInput) &&
        <div style={{ marginRight: '6px', display: 'flex', flexDirection: 'row', justifyContent: 'center', alignItems: 'center' }}>
          <span style={{ marginRight: '6px', fontSize: '14px', color: 'gray', whiteSpace: 'nowrap' }}>
            {loading && intl.formatMessage({ id: 'askAI_doing' })}
            {/* {!loading && extendInput && intl.formatMessage({ id: 'softbreak_tips' })} */}
          </span>
          {
            loading &&
            <ThreeDots
              height="8"
              width="30"
              radius="9"
              color='dodgerblue'
              ariaLabel='three-dots-loading'
            />
          }
        </div>
      }
    </div>), [loading, intl, textInputRef?.current, extendInput])

  React.useEffect(() => {
    if (textInputRef.current) {
      const range = document.createRange();
      const selection = window.getSelection();
      range.selectNodeContents(textInputRef.current);
      range.collapse(false);
      selection.removeAllRanges();
      selection.addRange(range);
    }
  }, [extendInput]);

  console.log('sub anchor................', sub_menu_anchor)

  return (
    <div
      style={{
        minWidth: '360px',
        width,
        backgroundColor: 'transparent',
        position: 'relative',
        cursor: 'move'
      }}
      onClick={() => handleClose(false, true)}
      ref={containerRef}
      // onBlur={() => {
      //   if (aiResponse || Boolean(sub_menu_visible)) return;
      //   textInputRef?.current?.focus();
      // }}
    >
      <div
        className='handle'
        style={{
          height: !!height ? height : undefined,
          position: 'relative'
        }}
        onClick={(e) => {
          e.stopPropagation();
        }}
        ref={mainDiv}
      >
        <div
          style={{
            ...styles.container,
            backgroundColor: 'white',
            paddingTop: (form && !aiResponse || selectedText?.trim()) ? 0 : 10,
          }}
          ref={mainInnerDiv}
        >
          {
            !!selectedText?.trim() &&
            <div style={{
              padding: '10px',
              paddingBottom: form && !aiResponse ? 0 : 10,
              // marginTop: 10,
              paddingTop: 10,
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              maxWidth: '100%'
            }}>
              <div style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                overflow: 'hidden',
              }}>
                <span style={{ color: 'dimgray', fontSize: 14, whiteSpace: 'nowrap' }}>
                  {intl.formatMessage({
                    id: selectedContent?.type == 'article' && 'selected_whole_page'
                      || selectedContent?.type == 'video' && 'selected_subtitles'
                      || selectedContent?.type == 'email' && 'selected_email'
                      || 'selected_text'
                  })}
                </span>
                <Tooltip title={selectedText} placement='top'>
                  <div style={{
                    backgroundColor: '#eaeaea',
                    fontSize: 14,
                    color: '#333',
                    padding: '2px',
                    paddingLeft: '4px',
                    paddingRight: '4px',
                    borderRadius: 5,
                    marginLeft: '4px',
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  }}>{selectedText}</div>
                </Tooltip>
              </div>
              {
                selectedText?.trim() &&
                <Tooltip
                  title={intl.formatMessage({ id: 'ask_ai_about_selected_tooltip' })}
                  placement='top'
                >
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'center',
                      cursor: 'pointer',
                      fontSize: 14,
                      color: !aiResponse && !loading && selected_content_chooseable ? (attach_selected_content && 'dodgerblue' || !attach_selected_content && '#333') : '#bbb',
                      columnGap: 4,
                      whiteSpace: 'nowrap'
                    }}
                    onClick={() => !aiResponse && !loading && selected_content_chooseable && set_attach_selected_content(prevState => !prevState)}
                  >
                    {
                      attach_selected_content &&
                      <CheckboxChecked size={18} />
                    }
                    {
                      !attach_selected_content &&
                      <CheckboxUnchecked size={18} />
                    }
                    {intl.formatMessage({ id: 'ask_ai_about_selected' })}
                  </div>
                </Tooltip>
              }
            </div>
          }
          {
            form && !aiResponse &&
            <div style={{
              padding: '10px',
              // paddingBottom: '0px',
              display: 'flex',
              flexDirection: 'column',
            }}>
              <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', whiteSpace: 'nowrap', columnGap: 6 }}>
                <div style={{
                  color: 'dimgray', fontSize: 14,
                }}>
                  {
                    intl.formatMessage({ id: 'ai_task' })
                  }
                </div>
                <div style={{ fontSize: 14, backgroundColor: '#eee', padding: '3px', paddingLeft: '6px', paddingRight: '6px', borderRadius: '4px' }}>
                  {form.item.label}
                </div>
                {
                  form.item.sub_items &&
                  <Selector
                    style={{
                      zIndex: 2147483647
                    }}
                    options={form.item.sub_items.map((item, index) => {
                      return item
                    })}
                    value={form.sub_item.value}
                    onChange={(value) => {
                      setForm({
                        ...form,
                        sub_item: form.item.sub_items.find(subItem => subItem.value === value)
                      })
                    }}
                  />
                }
                {
                  form.sub_item?.value === 'draft_more' &&
                  <input
                    ref={form_input_ref}
                    value={(form.sub_item.label === '...' ? '' : form.sub_item.label) || ''}
                    onChange={(event) => {
                      setForm({
                        ...form,
                        sub_item: { ...form.sub_item, label: event.target.value }
                      })
                    }}
                    style={{
                      padding: '4px',
                      marginLeft: '6px',
                      fontSize: '15px',
                      width: '200px',
                      border: '1px solid #ccc',
                      borderRadius: '4px',
                      backgroundColor: 'white',
                      outline: 'none',
                    }}
                    placeholder={intl.formatMessage({ id: 'draft_more_type' })}
                    autoFocus={true}
                  />
                }
              </div>
              {
                form.item?.subTasks?.filter(item => !item.to_arg)?.map((subTask) => {
                  return <div
                    key={subTask.label}
                    style={{ paddingTop: '8px', display: 'flex', flexDirection: 'row', alignItems: 'center', columnGap: '6px', marginBottom: '4px' }}>
                    {
                      subTask.title &&
                      <div style={{ color: '#222', fontSize: 15 }}>{subTask.title}</div>
                    }
                    {renderSubTaskButton(subTask)}
                  </div>
                })
              }
              {
                form.args.map((arg, index) => {
                  if (arg.disabled) {
                    return null
                  }

                  const subTask = form.item?.subTasks?.find(sub => sub.to_arg === arg.name);

                  return <div key={index} style={{ paddingTop: '8px' }}>
                    <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', columnGap: '6px', marginBottom: '4px' }}>
                      <div style={{ color: '#222', fontSize: 15 }}>{arg.label}</div>
                      {
                        !arg.readOnly &&
                        <div style={{ color: '#666', fontSize: '14px' }}> {'(' + intl.formatMessage({ id: arg.required ? 'arg_required' : 'arg_optional' }) + ')'} </div>
                      }
                      {
                        subTask && renderSubTaskButton(subTask)
                      }
                    </div>
                    {
                      arg.readOnly && arg.value &&
                      <div
                        style={{ userSelect: 'text', fontSize: 14, maxHeight: '200px', overflowY: 'auto', scrollbarColor: '#ddd white', backgroundColor: 'white', borderRadius: '6px', paddingLeft: '8px', paddingRight: '8px' }}
                      >
                        <div
                          className="transition-all duration-500"
                          dangerouslySetInnerHTML={{ __html: md.render(arg.value) }}
                        ></div>
                      </div>
                    }
                    {
                      ['text', 'textarea'].includes(arg.type) && !arg.readOnly &&
                      <textarea
                        ref={index === 0 ? form_input_ref : null}
                        rows={arg.name === 'other_reqs' && 3 || 5}
                        autoComplete='off'
                        placeholder={arg.hint}
                        className='fill-available'
                        style={{
                          border: 'solid 1px lightgray', borderRadius: '3px',
                          color: '#333', outline: 'none',
                          backgroundColor: 'white',
                          resize: 'vertical',
                          paddingLeft: '4px', paddingRight: '4px',
                          fontSize: 15
                        }}
                        value={arg.value || ''}
                        onChange={(event) => {
                          let args = [...form.args];
                          args[index] = {
                            ...args[index],
                            value: event.target.value
                          }

                          setForm({
                            ...form,
                            args
                          })
                        }}
                        onMouseDown={(event) => {
                          event.stopPropagation();
                        }}
                      />
                    }
                    {
                      ['textline', 'input'].includes(arg.type) && !arg.readOnly &&
                      <input
                        ref={index === 0 ? form_input_ref : null}
                        autoComplete='off'
                        placeholder={arg.hint}
                        className='fill-available'
                        style={{
                          border: 'solid 1px lightgray', borderRadius: '3px',
                          color: '#333', outline: 'none',
                          backgroundColor: 'white',
                          paddingLeft: '4px', paddingRight: '4px',
                          fontSize: 15, paddingTop: '4px', paddingBottom: '4px'
                        }}
                        value={arg.value || ''}
                        onChange={(event) => {
                          let args = [...form.args];
                          args[index] = {
                            ...args[index],
                            value: event.target.value
                          }

                          setForm({
                            ...form,
                            args
                          })
                        }}
                        onMouseDown={(event) => {
                          event.stopPropagation();
                        }}
                      />
                    }
                    {
                      arg.type === 'select' && !arg.readOnly &&
                      <div style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', whiteSpace: 'nowrap' }}>
                        <Selector
                          style={{
                            zIndex: 2147483647
                          }}
                          options={arg.options.map((option, index) => {
                            return option
                          })}
                          value={arg.value}
                          onChange={(value) => {
                            let args = [...form.args];
                            args[index] = {
                              ...args[index],
                              value
                            }

                            setForm({
                              ...form,
                              args
                            });
                          }}
                        />
                        {
                          (!arg.value || !arg.options.find(option => option.value === arg.value)) &&
                          <input
                            autoComplete='off'
                            placeholder={intl.formatMessage({ id: 'user_input_option' })}
                            className='fill-available'
                            style={{
                              border: 'solid 1px lightgray', borderRadius: '3px',
                              color: '#333', outline: 'none',
                              paddingLeft: '4px', paddingRight: '4px',
                              marginLeft: '8px',
                              fontSize: 15, paddingTop: '4px', paddingBottom: '4px'
                            }}
                            value={arg.value || ''}
                            onChange={(event) => {
                              let args = [...form.args];
                              args[index] = {
                                ...args[index],
                                value: event.target.value
                              }

                              setForm({
                                ...form,
                                args
                              })
                            }}
                          />
                        }
                      </div>
                    }
                  </div>
                })
              }
            </div>
          }

          {
            (aiResponse || errMsg) &&
            <div className="window"
              style={{
                cursor: 'default'
              }}
              onMouseDown={(event) => {
                setIsResizing(true);

                event.stopPropagation();
              }}
            >
              <div className="resizable-border"
                onMouseDown={(event) => {
                  event.stopPropagation();
                }}
                ref={resizableContainerRef}
              >
                <div
                  style={{
                    flex: 1, userSelect: 'text',
                    maxHeight: aiResponse ? main_inner_max_height : undefined,
                    overflowY: 'auto',
                    padding: '4px', paddingTop: '0px', paddingBottom: '0px',
                    marginLeft: 10,
                    marginRight: 10,
                    marginBottom: 10,
                    fontSize: 14,
                    scrollbarColor: '#ddd white',
                    backgroundColor: 'white', borderRadius: '6px',
                  }}
                  ref={contentContainerRef}
                >
                  {
                    !!aiResponse?.content &&
                    <div
                      ref={mdContainerRef}
                      className="transition-all duration-500"
                      dangerouslySetInnerHTML={{ __html: md.render(aiResponse.content) }}
                    ></div>
                  }

                  {
                    (errMsg || aiResponse?.err) &&
                    <div style={{ color: 'red', paddingBottom: 8 }}>
                      {
                        errMsg || aiResponse?.err
                      }
                    </div>
                  }
                </div>
              </div>
            </div>
          }

          {
            aiResponse?.err == 'exceed_msg_limit' &&
            <div style={{ display: 'flex', flexDirection: 'row', padding: '10px', alignItems: 'center', justifyContent: 'space-between' }}>
              <div style={{ paddingLeft: '16px', paddingRight: '16px', paddingTop: '4px', paddingBottom: '4px', cursor: 'pointer', color: 'white', fontWeight: 'bold', backgroundColor: 'dodgerblue', borderRadius: '6px' }} onClick={() => {
                window.open(`http://app.${funblocks_domain}/#/aiplans`)
              }}>{intl.formatMessage({ id: aiResponse.servingProduct?.productId == 'free' ? 'promote_trial' : 'upgrade_to_vip' })}</div>
              <div style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'flex-end'
              }} >
                <span style={{ marginLeft: '8px', marginRight: '4px', fontSize: '14px', color: 'gray' }}>{intl.formatMessage({ id: 'or_invite_friend_rewards' })}</span>
                <div style={{ paddingLeft: '16px', paddingRight: '16px', paddingTop: '4px', paddingBottom: '4px', cursor: 'pointer', color: 'white', fontWeight: 'bold', backgroundColor: 'limegreen', borderRadius: '6px' }} onClick={() => {
                  window.open(`http://app.${funblocks_domain}/#/invitation-event`)
                }}>{intl.formatMessage({ id: 'invite_friends' })}</div>
              </div>
            </div>
          }

          {
            !loading && aiResponse && !aiResponse.err &&
            <div style={{
              display: 'flex',
              flexDirection: 'row',
              paddingRight: '8px',
              paddingLeft: '8px',
              fontSize: '14px',
              paddingBottom: '8px'
            }}>
              <SharePopover
                shareData={shareData}
                beforeOnClick={() => new Promise((resolve) => {
                  setDoneMessage({
                    content: intl.formatMessage({ id: 'copied_to_share' })
                  });
                  setTimeout(() => {
                    setDoneMessage(null)
                    resolve("Shareit!");
                  }, 2000);
                })}
                toggleShare={toggleShare}
                handleClose={() => setShowSharePopover(false)}
                visible={showSharePopover}
              />
            </div>
          }

          <div style={{
            paddingLeft: '10px',
            paddingRight: '6px',
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'space-between',
            position: 'relative',
            // paddingTop: 10,
            paddingBottom: 10,
          }}>
            {
              !extendInput && ai_magic_icon_and_status
            }
            <div
              className='fill-available'
              style={{
                display: 'flex',
                flexDirection: extendInput ? 'column' : 'row',
                alignItems: extendInput ? undefined : 'center',
                justifyContent: 'flex-end',
              }}>
              {
                generator === 'mindmap' &&
                <div
                  className='fill-available'
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'flex-start',
                    marginBottom: 10,
                    columnGap: 15,
                  }}>
                  <div style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                    justifyContent: 'flex-start',
                  }}>
                    <div style={{
                      fontSize: 14,
                      color: '#999',
                      marginRight: 4,
                    }}>
                      {intl.formatMessage({ id: 'mindmap_application' })}
                    </div>
                    <Selector
                      options={mindmap_applications}
                      value={mindmap_application}
                      onChange={(value) => {
                        set_mindmap_application(value);
                      }}
                      inputStyle={{
                        borderRadius: 4,
                        maxWidth: showMentalModel ? 100 : undefined
                      }}
                    />
                  </div>
                  {
                    showMentalModel &&
                    <div style={{
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'flex-start',
                    }}>
                      <div style={{
                        fontSize: 14,
                        color: '#999',
                        marginRight: 4,
                      }}>
                        {intl.formatMessage({ id: 'mental_model' })}
                      </div>
                      <MentalModelSelector
                        app={mindmap_application}
                        value={mentalModel}
                        onSelect={(value) => setMentalModel(value)}
                        onInput={(event) => {
                          setMentalModel({
                            ...mentalModel,
                            inputValue: event.target.value
                          })
                        }} />
                    </div>
                  }
                </div>
              }
              {
                // !aiResponse && 
                !loading &&
                (!form || aiResponse) &&
                <div
                  style={{
                    position: 'relative',
                    width: '-webkit-fill-available',
                    alignItems: 'center',
                    borderRadius: 4,
                    padding: 6,
                    columnGap: 6,
                    paddingRight: 0,
                    paddingTop: 2,
                    paddingBottom: 2,
                    border: '1px solid #d8d8d8',
                    marginRight: 4,
                    backgroundColor: 'white'
                  }}
                  onKeyDown={(event) => onKeyDown(event, filteredAssistantItems)}
                  onKeyPress={(event) => onKeyPress(event, filteredAssistantItems)}
                  onMouseDown={(event) => {
                    event.stopPropagation();
                  }}
                >
                  <ContentEditable
                    innerRef={textInputRef}
                    style={{
                      // width: 'calc(100% - 32px)',
                      border: '0px',
                      outline: 'none',
                      fontSize: 15,
                      backgroundColor: 'white',
                      alignContent: extendInput ? undefined : 'flex-end',
                      textAlign: 'left',
                      cursor: 'text',
                      maxHeight: 360,
                      minHeight: extendInput ? 100 : undefined,
                      overflowY: 'auto',
                      paddingTop: 1,
                      paddingBottom: 1
                    }}
                    html={userInput || ''}
                    onChange={(event) => {
                      setUserInput(event.target.value);
                    }}

                    onCompositionStart={() => setIsInputZh(true)}
                    onCompositionEnd={() => setIsInputZh(false)}

                  // onPaste={(e) => {
                  //   e.preventDefault();

                  //   const text = e.clipboardData.getData('text/plain');
                  //   document.execCommand('insertText', false, text);
                  // }}
                  />

                  <span
                    className='fill-available'
                    style={{
                      position: 'absolute',
                      left: 6,
                      top: 4,
                      pointerEvents: 'none',
                      fontSize: 14,
                      color: '#bbb',
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    }}
                  >
                    {placeholder}
                  </span>
                </div>
              }
              <div
                // className={extendInput ? 'fill-available' : ''}
                style={{
                  display: 'flex',
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: extendInput ? 'space-between' : 'flex-end',
                  marginTop: extendInput ? 10 : undefined,
                }}>
                {
                  !!extendInput && ai_magic_icon_and_status
                }
                {
                  !loading &&
                  <div style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                    {
                      <ModelSelector
                        // dropdownIconSize={20}
                        inputStyle={{ marginRight: 4 }}
                        value={temp_llm_model}
                        onSelect={(value) => {
                          set_temp_llm_model(value);
                        }}
                        show_search_web_button={extendInput}
                      />
                    }
                    {
                      lng_list &&
                      <Selector
                        options={lng_list.filter(lng => !!lng).map(lang => {
                          return { label: lang.label, value: lang.Symbol }
                        })}
                        value={lang}
                        onChange={(value) => {
                          setLang(value)
                        }}
                        inputStyle={{
                          marginRight: 4,
                          borderRadius: 4,
                          maxWidth: extendInput ? 120 : 80
                        }}
                      // dropdownIconSize={20}
                      />
                    }
                    {
                      (!!searchText?.trim() || !aiResponse && form) && askAI_btn
                    }
                  </div>
                }
              </div>
            </div>
          </div>
          {
            aiResponse && <div style={{
              padding: '10px',
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              color: 'gray',
              backgroundColor: '#efefef',
              fontSize: 12,
              columnGap: 4
            }}>
              <Warning size={16} />
              {intl.formatMessage({ id: 'ai_response_warning' })}
            </div>
          }
        </div>
        <div style={{
          width: 16,
          height: 16,
          borderRadius: 20,
          position: 'absolute',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          right: -5,
          top: -8,
          color: 'white',
          backgroundColor: 'rgba(108,108,108, 0.6)',
          cursor: 'pointer',
          zIndex: 'inherit'
        }}
          onClick={() => {
            handleClose(false)
          }}
        >
          <Close size={12} />
        </div>
      </div>

      <div
        className='handle'
        style={{ ...styles.container, width: '320px', maxHeight: '330px', overflowY: 'auto' }}
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        {
          generator !== 'mindmap' && !confirmMessage && !doneMessage && groupedAssistantItems?.map(renderMenuItem)
        }
        {
          confirmMessage &&
          <ConfirmMessage
            content={confirmMessage.content}
            okText={confirmMessage.okText}
            onCancel={() => setConfirmMessage(null)}
            onConfirm={confirmMessage.onConfirm}
            onNeverShow={confirmMessage.onNeverShow}
            style={{
              padding: 10,
              paddingTop: 16
            }}
            optionSwitcher={confirm_option_switcher}
            optionEnter={confirm_option_enter}
          />
        }
        {
          doneMessage &&
          <div style={{
            padding: 10,
            fontSize: 14,
            color: '#444',
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            columnGap: '8px'
          }}>
            {doneMessage.content}
            <Check size={18} color='green' />
          </div>
        }
        <Popover
          open={Boolean(sub_menu_visible)}
          onClose={() => close_sub_menu()}
          onClick={(e) => {
            e.stopPropagation();
          }}
          anchorEl={sub_menu_anchor}
          anchorOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'left',
          }}
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            backgroundColor: 'transparent'
          }}
          // PaperProps={{
          //   style: {
          //     ...(modalPosition.x !== 0 || modalPosition.y !== 0 ? {
          //       position: 'fixed',
          //       left: modalPosition.x + 320,
          //       top: modalPosition.y,
          //       zIndex: 10000,
          //       transform: 'none'
          //     } : {})
          //   }
          // }}
        >
          <div
            style={{ ...styles.container, width: 200, margin: 0 }}
          >
            {
              sub_menu_items?.map((item, index) => {
                return <div
                  ref={(ref) => {
                    sub_menu_item_refs.current = { ...sub_menu_item_refs.current, [index]: ref };
                  }}
                  key={index + ''}
                >
                  <div
                    key={index + ''}
                    onClick={(e) => { sub_menu_item_clicked(item) }}
                    className='hoverStand'
                    style={{ backgroundColor: index === sub_menu_item_targeted ? 'rgb(212,228,247)' : undefined, flexDirection: 'row', justifyContent: 'space-between', padding: '5px', paddingLeft: '12px' }}
                  >
                    {item.label}
                    {index === sub_menu_item_targeted && <KeyboardReturn size={20} style={{ cursor: 'pointer', color: 'gray' }} />}
                  </div>
                </div>
              })
            }
          </div>
        </Popover>
      </div>
    </div>
  )
}

const styles = {
  container: {
    backgroundColor: 'white',
    boxShadow: '0px 0px 8px #bbb',
    margin: '5px',
    borderRadius: '5px',
    display: 'flex',
    flexDirection: 'column',
  },
  contentSect: {
    fontSize: 13,
    color: 'gray'
  }
}

export default AIForm;
