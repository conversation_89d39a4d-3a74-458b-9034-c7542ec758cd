import { MaterialSymbolsMoreHoriz } from '@/components/icon/more'
import { Popover } from '@mui/material'
import i18next from 'i18next'
import React, { useCallback, useRef, useState } from 'react'
import { RightArrowIcon } from '../../../../components/icon'

export type ListProps = {
  items: {
    label: React.ReactNode
    children: ListProps['items']
    icon: React.ReactNode
    instruction: string
  }[]
  onClick?: (item: ListProps['items'][number]) => void
  max?: number
}

export const List: React.FC<ListProps> = ({ items, onClick, max }) => {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null)
  const [moreAnchorEl, setMoreAnchorEl] = useState<HTMLElement | null>(null)
  const [openIndex, setOpenIndex] = useState<number | null>(null)
  const [moreOpen, setMoreOpen] = useState(false)

  const handleClick = useCallback(
    (item) => {
      if (item.children) {
        return
      }

      onClick?.(item)
    },
    [onClick]
  )

  const handlePopoverOpen = (event: React.MouseEvent<HTMLElement>, index: number) => {
    if (openIndex === index && anchorEl) {
      handlePopoverClose()
    } else {
      setAnchorEl(event.currentTarget)
      setOpenIndex(index)
    }
  }

  const handlePopoverClose = () => {
    setAnchorEl(null)
    setOpenIndex(null)
  }

  const handleMorePopoverOpen = (event: React.MouseEvent<HTMLElement>) => {
    if (moreOpen && moreAnchorEl) {
      handleMorePopoverClose()
    } else {
      setMoreAnchorEl(event.currentTarget)
      setMoreOpen(true)
    }
  }

  const handleMorePopoverClose = () => {
    setMoreAnchorEl(null)
    setMoreOpen(false)
  }

  const maxShownItem = typeof max === 'number' ? max : Infinity

  if (items.length === 0) {
    return null
  }

  const shouldShowMore = items.length > maxShownItem

  return (
    <div className="flex flex-col">
      {items.slice(0, maxShownItem).map((item, index) => {
        const itemEle = <Item item={item} onClick={handleClick} key={index} />

        if (!item.children?.length) {
          return itemEle
        }

        return (
          <div key={index}>
            <div onClick={(e) => handlePopoverOpen(e, index)}>
              {itemEle}
            </div>
            <Popover
              open={openIndex === index}
              anchorEl={anchorEl}
              onClose={handlePopoverClose}
              anchorOrigin={{
                vertical: 'center',
                horizontal: 'right',
              }}
              transformOrigin={{
                vertical: 'center',
                horizontal: 'left',
              }}
              disablePortal={false}
              container={() => document.body}
              style={{ zIndex: 2147483647 }}
              slotProps={{
                paper: {
                  style: {
                    zIndex: 2147483647,
                  }
                }
              }}
            >
              <List items={item.children} onClick={handleClick} />
            </Popover>
          </div>
        )
      })}
      {shouldShowMore ? (
        <div>
          <div onClick={handleMorePopoverOpen}>
            <Item
              item={{
                icon: <MaterialSymbolsMoreHoriz />,
                label: i18next.t('More'),
              }}
            />
          </div>
          <Popover
            open={moreOpen}
            anchorEl={moreAnchorEl}
            onClose={handleMorePopoverClose}
            anchorOrigin={{
              vertical: 'center',
              horizontal: 'left',
            }}
            transformOrigin={{
              vertical: 'center',
              horizontal: 'right',
            }}
            disablePortal={false}
            container={() => document.body}
            style={{ zIndex: 2147483647 }}
            slotProps={{
              paper: {
                style: {
                  zIndex: 2147483647,
                }
              }
            }}
          >
            <List items={items.slice(maxShownItem)} onClick={handleClick} />
          </Popover>
        </div>
      ) : null}
    </div>
  )
}

const Item: React.FC<{ item: any; onClick?: (item: any) => void }> = ({
  item,
  onClick,
}) => {
  const hasChildren = !!item.children?.length

  return (
    <div
      onClick={() => onClick?.(item)}
      className="h-7 hover:bg-zinc-200 rounded-none hover:rounded-md flex items-center justify-between text-[13px] hover:text-[14px] cursor-pointer px-1.5 transition-all duration-300"
    >
      <div className="flex items-center gap-1 overflow-hidden text-ellipsis whitespace-nowrap">
        <span className="text-amber-600 text-base h-4">{item.icon}</span>{' '}
        {item.label}
      </div>
      {hasChildren ? (
        <div className="w-3 rotate-90">
          <RightArrowIcon />
        </div>
      ) : null}
    </div>
  )
}
