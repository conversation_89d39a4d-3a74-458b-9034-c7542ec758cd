import { Button, Form } from 'antd'
import { useCallback, useEffect, useState } from 'react'
import { useSWRConfig } from 'swr'
import { useSettings } from '../../common/store/settings'

import i18next from 'i18next'
import Board from './board'
import { IntlProvider, useIntl } from 'react-intl'
import { Selector } from '@/common/selector'
import { funblocks_domain } from '@/common/serverAPIUtil'
import { EventName } from '@/common/event-name'
import { useDispatch } from 'react-redux'
import { getUserInfo } from '@/common/actions/ticketAction'
import browser from 'webextension-polyfill'
import { LlmAPIKeyModal } from '@/options/setting-form/llm-api-key-modal'

const LNGS = [
  {
    label: 'English',
    value: 'en',
  },
  {
    label: '中文',
    value: 'cn',
  },
]

export const Main: React.FC = () => {
  const { loading, settings, setSettings, refresh } = useSettings()
  const { mutate } = useSWRConfig()
  const intl = useIntl()
  const dispatch = useDispatch()

  useEffect(() => {
    const listener = (message) => {
      if (message.type === EventName.onTabActived) {
        refresh()
        dispatch(getUserInfo({}))
      }
    }

    browser.runtime.onMessage.addListener(listener)

    return () => browser.runtime.onMessage.removeListener(listener)
  }, [])

  useEffect(() => {
    if (!loading) {
      mutate('models')
    }
  }, [loading])

  if (loading) {
    return <div>loading...</div>
  }

  return (
    <div className="flex flex-col items-center" style={{ height: '100%' }}>
      <div
        className="flex justify-between lg:w-5/6 border-b pt-6 pb-1"
        style={{ alignItems: 'flex-center' }}
      >
        <div className="font-semibold text-3xl flex flex-row items-center">
          {i18next.t('FunBlocks AI')}
          <div className="text-xl pl-5">
            <a
              style={{ color: 'dodgerblue' }}
              href={`https://${funblocks_domain}`}
            >{`https://${funblocks_domain}`}</a>
          </div>
        </div>
        {/* <div>
          <div className="flex items-baseline text-xl gap-4">
            <a href="https://app.funblocks.net" target="_blank">
              <div className="p-2 rounded-sm hover:rounded-md bg-gray-50 hover:bg-gray-200 transition-all duration-300 cursor-pointer">
                Web App
              </div>
            </a>
          </div>
        </div> */}
      </div>

      <div className="flex flex-col lg:w-5/6 gap-4" style={{ height: '100%' }}>
        {/*  <OPENAISettings /> */}
        <Board />
        <LlmAPIKeyModal />
      </div>
    </div>
  )
}

const styles = {
  selector: {
    border: '1px solid #ccc',
    paddingLeft: 12,
    paddingRight: 6,
    borderRadius: 15,
  },
}
