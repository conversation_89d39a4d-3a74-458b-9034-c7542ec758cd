import { useEffect, useState, useCallback } from 'react';
import { Popover } from 'antd';
import { Tooltip } from '@mui/material';
import { KeyboardArrowDown } from '@styled-icons/material';

export const Selector = ({ options, value, onChange, triggerElement, showOpenIndicator, inputStyle, dropdownIconSize, placement, popoverStyle, extraMenuItems, disabled }) => {
    const [open, setOpen] = useState(false);
    const [hovered, setHovered] = useState();

    const hide = () => {
        setOpen(false);
    };

    const handleOpenChange = (newOpen) => {
        !disabled && setOpen(newOpen);
    };

    const handleItemClick = (value) => {
        hide();
        onChange(value);
    }

    const selectedOption = options && options.find(p => p.value === value) || {};

    return (<Popover
        overlayInnerStyle={{ padding: 0 }}
        arrow={false}
        trigger="click"
        open={!disabled && open}
        onOpenChange={handleOpenChange}
        placement={placement || "bottomLeft"}
        content={
            <div style={{
                maxHeight: 400,
                overflowY: 'auto',
                overflowX: 'hidden',
                borderRadius: 5,
                ...popoverStyle
            }}>
                {
                    options && options.map((option, index) => {
                        return (<Tooltip title={option.tips} placement='right'>
                            <div key={index + ''}>
                                {
                                    option.type === 'title' &&
                                    <>
                                        {
                                            !!index &&
                                            <div
                                                style={{
                                                    height: 1,
                                                    backgroundColor: '#ddd',
                                                    marginLeft: 6,
                                                    marginRight: 6
                                                }}
                                            />
                                        }
                                        <div
                                            style={{
                                                display: 'flex',
                                                flexDirection: 'row',
                                                alignItems: 'center',
                                                whiteSpace: 'nowrap',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                                color: 'gray',
                                                fontSize: 12,
                                                height: 28,
                                                paddingLeft: 6
                                            }}
                                        >
                                            {option.label}
                                        </div>
                                    </>
                                }
                                {
                                    option.type !== 'title' &&
                                    <div
                                        className='hoverStand'
                                        style={{
                                            display: 'flex',
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            justifyContent: 'space-between',
                                            width: '-webkit-fill-available',
                                            cursor: 'pointer',
                                            minWidth: 80,
                                            height: 30,
                                            padding: '8px',
                                            paddingTop: '2px',
                                            paddingBottom: '2px',
                                            borderRadius: popoverStyle?.borderRadius || 5,
                                            backgroundColor: option.value == value ? 'rgba(25,118,210, 0.2)' : (hovered == option.value ? '#eee' : undefined)
                                        }}
                                        value={option.value}
                                        onClick={(event) => {
                                            if (option.onClick) {
                                                option.onClick();
                                                return hide();
                                            }

                                            handleItemClick(option.value)
                                            event.preventDefault();
                                            event.stopPropagation();
                                        }}
                                        onMouseEnter={() => setHovered(option.value)}
                                        onMouseLeave={() => setHovered(null)}
                                    >
                                        <div
                                            style={{
                                                display: 'flex',
                                                flexDirection: 'row',
                                                alignItems: 'center',
                                                justifyContent: 'flex-start',
                                                columnGap: '6px',
                                                fontSize: '14px',
                                                color: '#333',
                                                whiteSpace: 'nowrap',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                            }}
                                        >
                                            {option.icon}
                                            <div style={{
                                                whiteSpace: 'nowrap',
                                                overflow: 'hidden',
                                                textOverflow: 'ellipsis',
                                            }}>{option.label}</div>
                                        </div>
                                        {
                                            hovered === option.value &&
                                            <div
                                                onClick={(event) => {
                                                    event.stopPropagation();
                                                    setAnchorEl(null);
                                                }}
                                            >{option.rightElement}
                                            </div>
                                        }
                                    </div>
                                }
                            </div>
                        </Tooltip>
                        );
                    })
                }
                {
                    extraMenuItems
                }
            </div>
        }
    >
        <div style={{
            position: 'relative'
        }}>
            {
                !!triggerElement && triggerElement
            }
            {
                !triggerElement &&
                <div
                    className='round-corner-input'
                    style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        cursor: 'pointer',
                        ...inputStyle
                    }}
                >
                    <div
                        style={{
                            display: 'inline',
                            columnGap: '6px',
                            fontSize: '14px',
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            color: disabled ? '#555' : '#333',
                        }}
                    >{selectedOption.icon} {selectedOption.label}</div>
                    <div style={{
                        width: dropdownIconSize || 17,
                        height: dropdownIconSize || 17,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                    }}>
                        <KeyboardArrowDown size={dropdownIconSize || 17} style={{ paddingLeft: 2, marginTop: 1, color: 'gray' }} />
                    </div>
                </div>
            }
            {
                // !!showOpenIndicator && 
                open && !inputStyle?.maxWidth &&
                <div
                    className='fill-available'
                    style={{
                        ...inputStyle,
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        height: '100%',
                        backgroundColor: 'rgba(0,0,0, 0.1)',
                        cursor: 'pointer'
                    }}>
                </div>
            }
        </div>
    </Popover>
    )
}


const styles = {
    menuItem: {
        display: 'flex',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    menuContent: {
        display: 'flex',
        width: '130px',
        margin: '0px',
        flexDirection: 'row',
        justifyContent: 'flex-start',
        alignContent: 'center',
        alignItems: 'center'
    },

    icon: {
        marginRight: 4
    }
}