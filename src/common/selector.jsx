import { useEffect, useState, useCallback } from 'react';
import { Tooltip } from '@mui/material';
import { KeyboardArrowDown } from '@styled-icons/material';
import CustomPopover from '@/components/CustomPopover';

export const Selector = ({ options, value, onChange, triggerElement, showOpenIndicator, inputStyle, dropdownIconSize, placement, popoverStyle, extraMenuItems, disabled }) => {
    const [open, setOpen] = useState(false);
    const [hovered, setHovered] = useState();
    const [anchorEl, setAnchorEl] = useState(null);

    const hide = () => {
        setOpen(false);
        setAnchorEl(null);
    };



    const handleItemClick = (value) => {
        hide();
        onChange(value);
    }

    const handleClick = (event) => {
        if (!disabled) {
            if (open) {
                hide();
            } else {
                setAnchorEl(event.currentTarget);
                setOpen(true);
            }
        }
    };

    const handleClose = (event, reason) => {
        // 防止点击触发元素时关闭
        if (reason === 'backdropClick' || reason === 'escapeKeyDown') {
            hide();
        }
    };

    const selectedOption = options && options.find(p => p.value === value) || {};

    // Convert antd placement to MUI anchorOrigin and transformOrigin
    const getAnchorOrigin = (placement) => {
        switch (placement) {
            case 'bottomLeft':
                return { vertical: 'bottom', horizontal: 'left' };
            case 'bottomRight':
                return { vertical: 'bottom', horizontal: 'right' };
            case 'topLeft':
                return { vertical: 'top', horizontal: 'left' };
            case 'topRight':
                return { vertical: 'top', horizontal: 'right' };
            case 'left':
                return { vertical: 'center', horizontal: 'left' };
            case 'right':
                return { vertical: 'center', horizontal: 'right' };
            default:
                return { vertical: 'bottom', horizontal: 'left' };
        }
    };

    const getTransformOrigin = (placement) => {
        switch (placement) {
            case 'bottomLeft':
                return { vertical: 'top', horizontal: 'left' };
            case 'bottomRight':
                return { vertical: 'top', horizontal: 'right' };
            case 'topLeft':
                return { vertical: 'bottom', horizontal: 'left' };
            case 'topRight':
                return { vertical: 'bottom', horizontal: 'right' };
            case 'left':
                return { vertical: 'center', horizontal: 'right' };
            case 'right':
                return { vertical: 'center', horizontal: 'left' };
            default:
                return { vertical: 'top', horizontal: 'left' };
        }
    };

    return (
        <>
            <CustomPopover
                open={!disabled && open}
                anchorEl={anchorEl}
                onClose={handleClose}
                anchorOrigin={getAnchorOrigin(placement || "bottomLeft")}
                transformOrigin={getTransformOrigin(placement || "bottomLeft")}
                paperStyle={{
                    padding: 0,
                    maxHeight: 400,
                    overflowY: 'auto',
                    overflowX: 'hidden',
                    borderRadius: 5,
                    ...popoverStyle
                }}
            >
                <div>
                    {
                        options && options.map((option, index) => {
                            return (<Tooltip title={option.tips || ''} placement='right' key={index + ''}>
                                <div>
                                    {
                                        option.type === 'title' &&
                                        <>
                                            {
                                                !!index &&
                                                <div
                                                    style={{
                                                        height: 1,
                                                        backgroundColor: '#ddd',
                                                        marginLeft: 6,
                                                        marginRight: 6
                                                    }}
                                                />
                                            }
                                            <div
                                                style={{
                                                    display: 'flex',
                                                    flexDirection: 'row',
                                                    alignItems: 'center',
                                                    whiteSpace: 'nowrap',
                                                    overflow: 'hidden',
                                                    textOverflow: 'ellipsis',
                                                    color: 'gray',
                                                    fontSize: 12,
                                                    height: 28,
                                                    paddingLeft: 6
                                                }}
                                            >
                                                {option.label}
                                            </div>
                                        </>
                                    }
                                    {
                                        option.type !== 'title' &&
                                        <div
                                            className='hoverStand'
                                            style={{
                                                display: 'flex',
                                                flexDirection: 'row',
                                                alignItems: 'center',
                                                justifyContent: 'space-between',
                                                width: '-webkit-fill-available',
                                                cursor: 'pointer',
                                                minWidth: 80,
                                                height: 30,
                                                padding: '8px',
                                                paddingTop: '2px',
                                                paddingBottom: '2px',
                                                borderRadius: popoverStyle?.borderRadius || 5,
                                                backgroundColor: option.value == value ? 'rgba(25,118,210, 0.2)' : (hovered == option.value ? '#eee' : undefined)
                                            }}
                                            value={option.value}
                                            onClick={(event) => {
                                                if (option.onClick) {
                                                    option.onClick();
                                                    return hide();
                                                }

                                                handleItemClick(option.value)
                                                event.preventDefault();
                                                event.stopPropagation();
                                            }}
                                            onMouseEnter={() => setHovered(option.value)}
                                            onMouseLeave={() => setHovered(null)}
                                        >
                                            <div
                                                style={{
                                                    display: 'flex',
                                                    flexDirection: 'row',
                                                    alignItems: 'center',
                                                    justifyContent: 'flex-start',
                                                    columnGap: '6px',
                                                    fontSize: '14px',
                                                    color: '#333',
                                                    whiteSpace: 'nowrap',
                                                    overflow: 'hidden',
                                                    textOverflow: 'ellipsis',
                                                }}
                                            >
                                                {option.icon}
                                                <div style={{
                                                    whiteSpace: 'nowrap',
                                                    overflow: 'hidden',
                                                    textOverflow: 'ellipsis',
                                                }}>{option.label}</div>
                                            </div>
                                            {
                                                hovered === option.value &&
                                                <div
                                                    onClick={(event) => {
                                                        event.stopPropagation();
                                                        hide();
                                                    }}
                                                >{option.rightElement}
                                                </div>
                                            }
                                        </div>
                                    }
                                </div>
                            </Tooltip>
                            );
                        })
                    }
                    {
                        extraMenuItems
                    }
                </div>
            </CustomPopover>
            <div
                style={{
                    position: 'relative'
                }}
                onClick={handleClick}
            >
                {
                    !!triggerElement && triggerElement
                }
                {
                    !triggerElement &&
                    <div
                        className='round-corner-input'
                        style={{
                            display: 'flex',
                            flexDirection: 'row',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            cursor: 'pointer',
                            ...inputStyle
                        }}
                    >
                        <div
                            style={{
                                display: 'inline',
                                columnGap: '6px',
                                fontSize: '14px',
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                color: disabled ? '#555' : '#333',
                            }}
                        >{selectedOption.icon} {selectedOption.label}</div>
                        <div style={{
                            width: dropdownIconSize || 17,
                            height: dropdownIconSize || 17,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                        }}>
                            <KeyboardArrowDown size={dropdownIconSize || 17} style={{ paddingLeft: 2, marginTop: 1, color: 'gray' }} />
                        </div>
                    </div>
                }
                {
                    // !!showOpenIndicator &&
                    open && !inputStyle?.maxWidth &&
                    <div
                        className='fill-available'
                        style={{
                            ...inputStyle,
                            position: 'absolute',
                            top: 0,
                            left: 0,
                            height: '100%',
                            backgroundColor: 'rgba(0,0,0, 0.1)',
                            cursor: 'pointer'
                        }}>
                    </div>
                }
            </div>
        </>
    )
}

